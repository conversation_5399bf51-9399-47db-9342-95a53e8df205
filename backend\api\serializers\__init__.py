# Import from module files
from .kpi_definitions import (
    TagSerializer, AcademicYearSerializer, ThemeSerializer, SubThemeSerializer,
    MeasurementUnitSerializer, KPISerializer
)

from .kpi_assignments import (
    UniversityKPIAssignmentSerializer, OfficeKPIAssignmentSerializer, UserKPIAssignmentSerializer,
    KPIReportSerializer, OfficeKPIReportSerializer, UserKPIReportSerializer
)

# Import from parent serializers.py file
import os
import importlib.util

# Get the parent directory path
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
serializers_path = os.path.join(parent_dir, 'serializers.py')

# Check if serializers.py exists
if os.path.exists(serializers_path):
    # Import directly from the file
    spec = importlib.util.spec_from_file_location('api.serializers_module', serializers_path)
    serializers_module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(serializers_module)

    # Import the missing serializers
    UserSerializer = serializers_module.UserSerializer
    UniversitySerializer = serializers_module.UniversitySerializer
    DomainSerializer = serializers_module.DomainSerializer
    UniversityClassificationSerializer = serializers_module.UniversityClassificationSerializer
    OfficeSerializer = serializers_module.OfficeSerializer
    UserPositionSerializer = serializers_module.UserPositionSerializer
    AuditLogSerializer = serializers_module.AuditLogSerializer

# Define __all__ to explicitly export these symbols
__all__ = [
    'TagSerializer', 'AcademicYearSerializer', 'ThemeSerializer', 'SubThemeSerializer',
    'MeasurementUnitSerializer', 'KPISerializer', 'UniversityKPIAssignmentSerializer',
    'OfficeKPIAssignmentSerializer', 'UserKPIAssignmentSerializer', 'KPIReportSerializer',
    'OfficeKPIReportSerializer', 'UserKPIReportSerializer', 'UserSerializer',
    'UniversitySerializer', 'DomainSerializer', 'UniversityClassificationSerializer',
    'OfficeSerializer', 'UserPositionSerializer', 'AuditLogSerializer'
]