import { useState, useEffect } from 'react'
import {
  Container,
  Grid,
  Paper,
  Typography,
  Box,
  CircularProgress,
  Alert,
  Card,
  CardContent,
  useTheme,
  Divider,
  Chip,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Avatar,
  LinearProgress,
  Tooltip,
  IconButton,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Badge,
} from '@mui/material'
import { alpha } from '@mui/material/styles'
import SchoolIcon from '@mui/icons-material/School'
import TimelineIcon from '@mui/icons-material/Timeline'
import AssignmentIcon from '@mui/icons-material/Assignment'
import TrendingUpIcon from '@mui/icons-material/TrendingUp'
import CategoryIcon from '@mui/icons-material/Category'
import CalendarMonthIcon from '@mui/icons-material/CalendarMonth'
import WarningIcon from '@mui/icons-material/Warning'
import CheckCircleIcon from '@mui/icons-material/CheckCircle'
import ErrorIcon from '@mui/icons-material/Error'
import InfoIcon from '@mui/icons-material/Info'
import RefreshIcon from '@mui/icons-material/Refresh'
import VisibilityIcon from '@mui/icons-material/Visibility'
import StarIcon from '@mui/icons-material/Star'
import TrendingDownIcon from '@mui/icons-material/TrendingDown'
import { BarChart, PieChart, LineChart } from '@mui/x-charts'
import TenantBanner from './TenantBanner'
import { getMoEDashboard } from '../../services/dashboardService'
import api from '../../services/api'

const PublicTenantDashboard = () => {
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const [dashboardData, setDashboardData] = useState(null)
  const [performanceData, setPerformanceData] = useState(null)
  const [universityPerformance, setUniversityPerformance] = useState([])
  const [themePerformance, setThemePerformance] = useState([])
  const [refreshing, setRefreshing] = useState(false)
  const theme = useTheme()

  // Mock data for demonstration
  const mockUniversityData = [
    { name: 'Addis Ababa University', performance: 92.5, status: 'Excellent', kpis: 45, offices: 30, trend: 'up' },
    { name: 'Bahir Dar University', performance: 88.3, status: 'Excellent', kpis: 42, offices: 25, trend: 'up' },
    { name: 'Jimma University', performance: 85.7, status: 'Good', kpis: 38, offices: 22, trend: 'stable' },
    { name: 'Hawassa University', performance: 82.1, status: 'Good', kpis: 35, offices: 20, trend: 'up' },
    { name: 'Mekelle University', performance: 79.4, status: 'Good', kpis: 33, offices: 18, trend: 'down' },
    { name: 'Haramaya University', performance: 76.8, status: 'Needs Attention', kpis: 31, offices: 16, trend: 'stable' },
    { name: 'Arba Minch University', performance: 74.2, status: 'Needs Attention', kpis: 28, offices: 15, trend: 'up' },
    { name: 'University of Gondar', performance: 71.5, status: 'Needs Attention', kpis: 25, offices: 12, trend: 'down' },
  ]

  const mockThemeData = [
    { name: 'Academic Excellence', performance: 85.2, universities: 8, kpis: 25, color: '#1976d2' },
    { name: 'Research & Innovation', performance: 78.9, universities: 8, kpis: 18, color: '#2e7d32' },
    { name: 'Student Success', performance: 82.4, universities: 8, kpis: 22, color: '#ed6c02' },
    { name: 'Infrastructure', performance: 76.1, universities: 8, kpis: 15, color: '#9c27b0' },
    { name: 'Financial Management', performance: 88.7, universities: 8, kpis: 12, color: '#d32f2f' },
    { name: 'Human Resources', performance: 81.3, universities: 8, kpis: 16, color: '#0288d1' },
  ]

  const fetchDashboardData = async () => {
    try {
      setLoading(true)
      
      // Fetch basic dashboard data
      const data = await getMoEDashboard()
      setDashboardData(data)

      // Fetch performance analytics
      try {
        const performanceResponse = await api.get('/analytics/performance-dashboard/')
        setPerformanceData(performanceResponse.data)
        setUniversityPerformance(performanceResponse.data.university_performance || mockUniversityData)
        setThemePerformance(performanceResponse.data.theme_performance || mockThemeData)
      } catch (perfError) {
        console.warn('Using mock performance data:', perfError)
        setUniversityPerformance(mockUniversityData)
        setThemePerformance(mockThemeData)
      }

      setError(null)
    } catch (err) {
      console.error('Error fetching dashboard data:', err)
      setError('Failed to load dashboard data. Using sample data for demonstration.')
      
      // Use mock data when API fails
      setDashboardData({
        counts: { universities: 8, kpis: 156, assignments: 1131, themes: 6 },
        performance: 82.3
      })
      setUniversityPerformance(mockUniversityData)
      setThemePerformance(mockThemeData)
    } finally {
      setLoading(false)
    }
  }

  const handleRefresh = async () => {
    setRefreshing(true)
    await fetchDashboardData()
    setRefreshing(false)
  }

  useEffect(() => {
    fetchDashboardData()
  }, [])

  const getStatusColor = (status) => {
    switch (status) {
      case 'Excellent': return '#4caf50'
      case 'Good': return '#ff9800'
      case 'Needs Attention': return '#f44336'
      default: return '#9e9e9e'
    }
  }

  const getTrendIcon = (trend) => {
    switch (trend) {
      case 'up': return <TrendingUpIcon sx={{ color: '#4caf50', fontSize: 16 }} />
      case 'down': return <TrendingDownIcon sx={{ color: '#f44336', fontSize: 16 }} />
      default: return <InfoIcon sx={{ color: '#9e9e9e', fontSize: 16 }} />
    }
  }

  if (loading) {
    return (
      <Container maxWidth="xl" sx={{ mt: 4, mb: 4 }}>
        <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
          <CircularProgress size={60} />
        </Box>
      </Container>
    )
  }

  return (
    <Container maxWidth="xl" sx={{ mt: 4, mb: 4 }}>
      <TenantBanner />
      
      {error && (
        <Alert severity="warning" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* Header with Refresh */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Typography variant="h4" component="h1" sx={{ fontWeight: 'bold', color: theme.palette.primary.main }}>
          Ministry of Education Dashboard
        </Typography>
        <Button
          variant="outlined"
          startIcon={<RefreshIcon />}
          onClick={handleRefresh}
          disabled={refreshing}
          sx={{ borderRadius: 2 }}
        >
          {refreshing ? 'Refreshing...' : 'Refresh Data'}
        </Button>
      </Box>

      {/* Key Metrics Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <StatsCard
            title="Universities"
            value={dashboardData?.counts?.universities || 8}
            icon={<SchoolIcon />}
            color="#1976d2"
            subtitle="Higher Education Institutions"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatsCard
            title="Active KPIs"
            value={dashboardData?.counts?.kpis || 156}
            icon={<TimelineIcon />}
            color="#2e7d32"
            subtitle="Performance Indicators"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatsCard
            title="Assignments"
            value={dashboardData?.counts?.assignments || 1131}
            icon={<AssignmentIcon />}
            color="#ed6c02"
            subtitle="KPI Assignments"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatsCard
            title="Overall Performance"
            value={`${dashboardData?.performance || 82.3}%`}
            icon={<TrendingUpIcon />}
            color="#9c27b0"
            subtitle="System-wide Average"
          />
        </Grid>
      </Grid>

      {/* University Performance Overview */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} lg={8}>
          <Paper sx={{ p: 3, height: '100%' }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
              <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
                University Performance Rankings
              </Typography>
              <Chip 
                label={`${universityPerformance.length} Universities`} 
                color="primary" 
                variant="outlined" 
              />
            </Box>
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Rank</TableCell>
                    <TableCell>University</TableCell>
                    <TableCell align="center">Performance</TableCell>
                    <TableCell align="center">Status</TableCell>
                    <TableCell align="center">KPIs</TableCell>
                    <TableCell align="center">Trend</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {universityPerformance.map((university, index) => (
                    <TableRow key={university.name} hover>
                      <TableCell>
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          {index < 3 && <StarIcon sx={{ color: '#ffd700', mr: 1 }} />}
                          #{index + 1}
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <Avatar sx={{ bgcolor: theme.palette.primary.main, mr: 2, width: 32, height: 32 }}>
                            <SchoolIcon fontSize="small" />
                          </Avatar>
                          <Typography variant="body2" sx={{ fontWeight: 500 }}>
                            {university.name}
                          </Typography>
                        </Box>
                      </TableCell>
                      <TableCell align="center">
                        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                          <Typography variant="body2" sx={{ fontWeight: 'bold', mr: 1 }}>
                            {university.performance}%
                          </Typography>
                          <LinearProgress
                            variant="determinate"
                            value={university.performance}
                            sx={{ width: 60, height: 6, borderRadius: 3 }}
                          />
                        </Box>
                      </TableCell>
                      <TableCell align="center">
                        <Chip
                          label={university.status}
                          size="small"
                          sx={{
                            backgroundColor: alpha(getStatusColor(university.status), 0.1),
                            color: getStatusColor(university.status),
                            fontWeight: 'bold'
                          }}
                        />
                      </TableCell>
                      <TableCell align="center">
                        <Badge badgeContent={university.offices} color="secondary">
                          <Chip label={university.kpis} size="small" variant="outlined" />
                        </Badge>
                      </TableCell>
                      <TableCell align="center">
                        {getTrendIcon(university.trend)}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </Paper>
        </Grid>

        <Grid item xs={12} lg={4}>
          <Paper sx={{ p: 3, height: '100%' }}>
            <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 3 }}>
              Performance Distribution
            </Typography>
            <PieChart
              series={[
                {
                  data: [
                    { id: 0, value: universityPerformance.filter(u => u.status === 'Excellent').length, label: 'Excellent', color: '#4caf50' },
                    { id: 1, value: universityPerformance.filter(u => u.status === 'Good').length, label: 'Good', color: '#ff9800' },
                    { id: 2, value: universityPerformance.filter(u => u.status === 'Needs Attention').length, label: 'Needs Attention', color: '#f44336' },
                  ],
                  innerRadius: 40,
                  outerRadius: 80,
                  paddingAngle: 2,
                  cornerRadius: 4,
                }
              ]}
              width={300}
              height={200}
            />
          </Paper>
        </Grid>
      </Grid>

      {/* Theme Performance Analysis */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 3 }}>
              Performance by Strategic Themes
            </Typography>
            <Grid container spacing={3}>
              {themePerformance.map((theme, index) => (
                <Grid item xs={12} sm={6} md={4} key={theme.name}>
                  <Card sx={{
                    height: '100%',
                    border: `2px solid ${alpha(theme.color, 0.2)}`,
                    '&:hover': {
                      borderColor: theme.color,
                      transform: 'translateY(-2px)',
                      transition: 'all 0.2s'
                    }
                  }}>
                    <CardContent>
                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                        <Box sx={{
                          width: 12,
                          height: 12,
                          borderRadius: '50%',
                          backgroundColor: theme.color,
                          mr: 2
                        }} />
                        <Typography variant="h6" sx={{ fontWeight: 600, fontSize: '1rem' }}>
                          {theme.name}
                        </Typography>
                      </Box>

                      <Box sx={{ mb: 2 }}>
                        <Typography variant="h4" sx={{ fontWeight: 'bold', color: theme.color }}>
                          {theme.performance}%
                        </Typography>
                        <LinearProgress
                          variant="determinate"
                          value={theme.performance}
                          sx={{
                            height: 8,
                            borderRadius: 4,
                            backgroundColor: alpha(theme.color, 0.1),
                            '& .MuiLinearProgress-bar': {
                              backgroundColor: theme.color,
                              borderRadius: 4
                            }
                          }}
                        />
                      </Box>

                      <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 2 }}>
                        <Tooltip title="Universities participating">
                          <Chip
                            icon={<SchoolIcon />}
                            label={theme.universities}
                            size="small"
                            variant="outlined"
                          />
                        </Tooltip>
                        <Tooltip title="KPIs in this theme">
                          <Chip
                            icon={<TimelineIcon />}
                            label={theme.kpis}
                            size="small"
                            variant="outlined"
                          />
                        </Tooltip>
                      </Box>
                    </CardContent>
                  </Card>
                </Grid>
              ))}
            </Grid>
          </Paper>
        </Grid>
      </Grid>

      {/* Performance Trends and Analytics */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3, height: 400 }}>
            <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 3 }}>
              University Performance Comparison
            </Typography>
            <BarChart
              xAxis={[{
                scaleType: 'band',
                data: universityPerformance.slice(0, 5).map(u => u.name.split(' ')[0])
              }]}
              series={[{
                data: universityPerformance.slice(0, 5).map(u => u.performance),
                color: '#1976d2'
              }]}
              width={400}
              height={300}
            />
          </Paper>
        </Grid>

        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3, height: 400 }}>
            <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 3 }}>
              Theme Performance Overview
            </Typography>
            <BarChart
              xAxis={[{
                scaleType: 'band',
                data: themePerformance.map(t => t.name.split(' ')[0])
              }]}
              series={[{
                data: themePerformance.map(t => t.performance),
                color: '#2e7d32'
              }]}
              width={400}
              height={300}
            />
          </Paper>
        </Grid>
      </Grid>

      {/* Action Items and Alerts */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3, height: '100%' }}>
            <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 3, color: '#f44336' }}>
              <WarningIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
              Universities Needing Attention
            </Typography>
            <List>
              {universityPerformance
                .filter(u => u.status === 'Needs Attention')
                .map((university) => (
                  <ListItem key={university.name} sx={{ px: 0 }}>
                    <ListItemIcon>
                      <ErrorIcon sx={{ color: '#f44336' }} />
                    </ListItemIcon>
                    <ListItemText
                      primary={university.name}
                      secondary={`Performance: ${university.performance}% - Requires immediate support`}
                    />
                    <Button
                      size="small"
                      variant="outlined"
                      startIcon={<VisibilityIcon />}
                      sx={{ ml: 2 }}
                    >
                      View Details
                    </Button>
                  </ListItem>
                ))}
            </List>
          </Paper>
        </Grid>

        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3, height: '100%' }}>
            <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 3, color: '#4caf50' }}>
              <CheckCircleIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
              Top Performing Universities
            </Typography>
            <List>
              {universityPerformance
                .filter(u => u.status === 'Excellent')
                .slice(0, 3)
                .map((university, index) => (
                  <ListItem key={university.name} sx={{ px: 0 }}>
                    <ListItemIcon>
                      <StarIcon sx={{ color: '#ffd700' }} />
                    </ListItemIcon>
                    <ListItemText
                      primary={university.name}
                      secondary={`Performance: ${university.performance}% - Exemplary achievement`}
                    />
                    <Chip
                      label={`#${index + 1}`}
                      size="small"
                      sx={{
                        backgroundColor: '#4caf50',
                        color: 'white',
                        fontWeight: 'bold'
                      }}
                    />
                  </ListItem>
                ))}
            </List>
          </Paper>
        </Grid>
      </Grid>

      {/* System Health and Quick Actions */}
      <Grid container spacing={3}>
        <Grid item xs={12} md={8}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 3 }}>
              System Health Indicators
            </Typography>
            <Grid container spacing={2}>
              {[
                { label: 'Data Collection', status: 'Active', color: '#4caf50' },
                { label: 'Performance Tracking', status: 'Operational', color: '#4caf50' },
                { label: 'Real-time Monitoring', status: 'Enabled', color: '#4caf50' },
                { label: 'Benchmarking', status: 'Active', color: '#4caf50' },
                { label: 'Strategic Planning', status: 'Data-Driven', color: '#2e7d32' },
                { label: 'Accountability', status: 'Transparent', color: '#1976d2' },
              ].map((indicator) => (
                <Grid item xs={12} sm={6} md={4} key={indicator.label}>
                  <Box sx={{
                    p: 2,
                    border: `1px solid ${alpha(indicator.color, 0.3)}`,
                    borderRadius: 2,
                    backgroundColor: alpha(indicator.color, 0.05)
                  }}>
                    <Typography variant="body2" sx={{ fontWeight: 600, mb: 1 }}>
                      {indicator.label}
                    </Typography>
                    <Chip
                      label={indicator.status}
                      size="small"
                      sx={{
                        backgroundColor: indicator.color,
                        color: 'white',
                        fontWeight: 'bold'
                      }}
                    />
                  </Box>
                </Grid>
              ))}
            </Grid>
          </Paper>
        </Grid>

        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 3, height: '100%' }}>
            <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 3 }}>
              Quick Actions
            </Typography>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
              {[
                { label: 'Generate System Report', icon: <AssignmentIcon />, color: '#1976d2' },
                { label: 'View Detailed Analytics', icon: <TimelineIcon />, color: '#2e7d32' },
                { label: 'Export Performance Data', icon: <TrendingUpIcon />, color: '#ed6c02' },
                { label: 'Schedule Review Meeting', icon: <CalendarMonthIcon />, color: '#9c27b0' },
              ].map((action) => (
                <Button
                  key={action.label}
                  variant="outlined"
                  startIcon={action.icon}
                  fullWidth
                  sx={{
                    justifyContent: 'flex-start',
                    borderColor: alpha(action.color, 0.5),
                    color: action.color,
                    '&:hover': {
                      borderColor: action.color,
                      backgroundColor: alpha(action.color, 0.05)
                    }
                  }}
                >
                  {action.label}
                </Button>
              ))}
            </Box>
          </Paper>
        </Grid>
      </Grid>
    </Container>
  )
}

const StatsCard = ({ title, value, icon, color, subtitle }) => {
  const theme = useTheme()
  
  return (
    <Card sx={{
      height: '100%',
      background: `linear-gradient(135deg, ${alpha(color, 0.1)} 0%, ${alpha(color, 0.05)} 100%)`,
      border: `1px solid ${alpha(color, 0.2)}`,
      transition: 'transform 0.2s, box-shadow 0.2s',
      '&:hover': { 
        transform: 'translateY(-4px)',
        boxShadow: `0 8px 25px ${alpha(color, 0.15)}`
      }
    }}>
      <CardContent sx={{ p: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
          <Box sx={{
            p: 1.5,
            borderRadius: 2,
            backgroundColor: alpha(color, 0.1),
            color: color,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}>
            {icon}
          </Box>
        </Box>
        <Typography variant="h3" component="div" sx={{ 
          fontWeight: 'bold', 
          color: color,
          mb: 1
        }}>
          {value}
        </Typography>
        <Typography variant="h6" sx={{ fontWeight: 600, mb: 0.5 }}>
          {title}
        </Typography>
        <Typography variant="body2" color="text.secondary">
          {subtitle}
        </Typography>
      </CardContent>
    </Card>
  )
}

export default PublicTenantDashboard
