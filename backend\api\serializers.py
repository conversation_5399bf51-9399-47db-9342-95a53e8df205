from rest_framework import serializers
from django.contrib.auth.models import User
from django.db import models
from universities.models import University, Domain, UniversityClassification
from offices.models import Office, UserPosition, AuditLog, UserProfile
from kpi_definitions.models import (
    Tag, AcademicYear, Theme, SubTheme, MeasurementUnit, KPI
)
from kpi_assignments.models import (
    UniversityKPIAssignment, OfficeKPIAssignment, UserKPIAssignment,
    KPIReport, OfficeKPIReport, UserKPIReport
)


class UserProfileSerializer(serializers.ModelSerializer):
    """Serializer for UserProfile model."""

    class Meta:
        model = UserProfile
        fields = ['office', 'phone', 'position', 'is_manager', 'profile_image', 'address', 'is_active']

    def to_representation(self, instance):
        """Handle cases where the profile might be None."""
        if instance is None:
            # Return default empty values if profile doesn't exist
            return {
                'office': None,
                'phone': None,
                'position': None,
                'is_manager': False,
                'profile_image': None,
                'address': None,
                'is_active': True
            }
        return super().to_representation(instance)


class UserSerializer(serializers.ModelSerializer):
    """Serializer for User model."""
    # Add tenant information
    tenant = serializers.SerializerMethodField()
    university = serializers.SerializerMethodField()

    # Add profile information
    profile = UserProfileSerializer(required=False)

    # Add password field for user creation
    password = serializers.CharField(write_only=True, required=False, style={'input_type': 'password'})
    password_confirm = serializers.CharField(write_only=True, required=False, style={'input_type': 'password'})

    def to_representation(self, instance):
        """Handle cases where a user doesn't have a profile."""
        ret = super().to_representation(instance)

        # Check if profile exists, if not, provide default empty values
        if 'profile' not in ret or ret['profile'] is None:
            try:
                # Try to get the profile
                if hasattr(instance, 'profile'):
                    ret['profile'] = UserProfileSerializer(instance.profile).data
                else:
                    # Create a default profile representation
                    ret['profile'] = {
                        'office': None,
                        'phone': None,
                        'position': None,
                        'is_manager': False,
                        'profile_image': None,
                        'address': None,
                        'is_active': True
                    }
            except Exception:
                # If any error occurs, provide default values
                ret['profile'] = {
                    'office': None,
                    'phone': None,
                    'position': None,
                    'is_manager': False,
                    'profile_image': None,
                    'address': None,
                    'is_active': True
                }

        return ret

    class Meta:
        model = User
        fields = [
            'id', 'username', 'email', 'password', 'password_confirm',
            'first_name', 'last_name', 'is_staff', 'is_active',
            'tenant', 'university', 'profile', 'date_joined', 'last_login'
        ]
        read_only_fields = ['is_staff', 'tenant', 'university', 'date_joined', 'last_login']

    def get_tenant(self, obj):
        """Get the tenant (schema_name) for the user."""
        from django.db import connection
        return connection.schema_name

    def get_university(self, obj):
        """Get the university information for the user."""
        from django.db import connection
        from universities.models import University

        try:
            university = University.objects.get(schema_name=connection.schema_name)
            return {
                'id': university.id,
                'name': university.name,
                'abbreviation': university.abbreviation,
                'schema_name': university.schema_name,
                'logo_url': university.logo.url if university.logo else None,
                'domains': [{
                    'domain': domain.domain,
                    'is_primary': domain.is_primary
                } for domain in university.domains.all()]
            }
        except University.DoesNotExist:
            return None

    def validate(self, data):
        """Validate the data."""
        # Check if this is a create operation (password is required)
        if self.instance is None and not self.context.get('partial', False):
            if not data.get('password'):
                raise serializers.ValidationError({'password': 'Password is required when creating a user.'})
            if not data.get('password_confirm'):
                raise serializers.ValidationError({'password_confirm': 'Password confirmation is required.'})
            if data.get('password') != data.get('password_confirm'):
                raise serializers.ValidationError({'password_confirm': 'Passwords do not match.'})

        # If password is being updated, validate it
        elif 'password' in data:
            if not data.get('password_confirm'):
                raise serializers.ValidationError({'password_confirm': 'Password confirmation is required.'})
            if data.get('password') != data.get('password_confirm'):
                raise serializers.ValidationError({'password_confirm': 'Passwords do not match.'})

        return data

    def create(self, validated_data):
        """Create a new user with profile."""
        # Remove profile data and password confirmation
        profile_data = validated_data.pop('profile', {})
        validated_data.pop('password_confirm', None)

        # Create the user with the password
        password = validated_data.pop('password', None)
        user = User.objects.create(**validated_data)
        if password:
            user.set_password(password)
            user.save()

        # Update profile if provided
        if profile_data and hasattr(user, 'profile'):
            for attr, value in profile_data.items():
                setattr(user.profile, attr, value)
            user.profile.save()

        return user

    def update(self, instance, validated_data):
        """Update a user with profile."""
        # Remove profile data and password confirmation
        profile_data = validated_data.pop('profile', {})
        validated_data.pop('password_confirm', None)

        # Update password if provided
        password = validated_data.pop('password', None)
        if password:
            instance.set_password(password)

        # Update user fields
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()

        # Update profile if provided
        if profile_data and hasattr(instance, 'profile'):
            for attr, value in profile_data.items():
                setattr(instance.profile, attr, value)
            instance.profile.save()

        return instance


class UniversityClassificationSerializer(serializers.ModelSerializer):
    """Serializer for UniversityClassification model."""

    class Meta:
        model = UniversityClassification
        fields = ['id', 'name', 'description', 'created_on', 'updated_on']
        read_only_fields = ['created_on', 'updated_on']


class DomainSerializer(serializers.ModelSerializer):
    """Serializer for Domain model."""

    class Meta:
        model = Domain
        fields = ['id', 'domain', 'is_primary', 'tenant']
        read_only_fields = ['tenant']


class UniversitySerializer(serializers.ModelSerializer):
    """Serializer for University model."""
    domains = DomainSerializer(many=True, read_only=True)
    logo_url = serializers.SerializerMethodField()
    banner_image_url = serializers.SerializerMethodField()
    created_by = serializers.SerializerMethodField()
    classification_details = UniversityClassificationSerializer(source='classification', read_only=True)
    classification_name = serializers.SerializerMethodField()

    # Admin user fields
    admin_username = serializers.CharField(required=False)
    admin_email = serializers.EmailField(required=False)
    admin_created = serializers.BooleanField(read_only=True)
    temporary_admin_password = serializers.CharField(required=False, write_only=True, style={'input_type': 'password'})

    class Meta:
        model = University
        fields = [
            'id', 'schema_name', 'name', 'abbreviation', 'motto', 'logo', 'logo_url',
            'banner_image', 'banner_image_url',
            'classification', 'classification_details', 'classification_name',
            'address_phone', 'address_email', 'address_website',
            'address_street', 'address_city', 'address_pobox',
            'created_on', 'updated_on', 'created_by', 'domains',
            # Admin user fields
            'admin_username', 'admin_email', 'admin_created', 'temporary_admin_password'
        ]
        read_only_fields = ['created_on', 'updated_on', 'logo_url', 'banner_image_url', 'admin_created', 'created_by', 'classification_details', 'classification_name']

    def get_logo_url(self, obj):
        """Get the URL for the logo."""
        if obj.logo and hasattr(obj.logo, 'url'):
            return obj.logo.url
        return None

    def get_banner_image_url(self, obj):
        """Get the URL for the banner image."""
        if obj.banner_image and hasattr(obj.banner_image, 'url'):
            return obj.banner_image.url
        return None

    def get_created_by(self, obj):
        """Get the username of the user who created the university."""
        # Since we don't have a direct created_by field in the model,
        # we'll check the audit logs or return a default value
        try:
            from offices.models import AuditLog
            # Look for the creation log entry
            creation_log = AuditLog.objects.filter(
                content_type='university',  # content_type is a CharField, not a relation
                object_id=obj.id,
                action='create'
            ).order_by('timestamp').first()

            if creation_log and creation_log.user:
                return creation_log.user.username

            # If no specific log found, return the superuser who likely created it
            from django.contrib.auth.models import User
            superuser = User.objects.filter(is_superuser=True).first()
            if superuser:
                return superuser.username
        except Exception as e:
            # Log the error but don't fail
            print(f"Error getting created_by for university {obj.id}: {e}")

        return "System Administrator"

    def get_classification_name(self, obj):
        """Get the name of the university classification."""
        if obj.classification:
            try:
                # Check if classification is already an object with a name attribute
                if hasattr(obj.classification, 'name'):
                    return obj.classification.name

                # If classification is just an ID, try to fetch the object
                try:
                    from universities.models import UniversityClassification
                    # Handle both integer and string IDs
                    classification_id = int(obj.classification) if isinstance(obj.classification, (int, str)) else obj.classification
                    classification = UniversityClassification.objects.get(id=classification_id)
                    return classification.name
                except (UniversityClassification.DoesNotExist, ValueError, TypeError, Exception) as e:
                    print(f"Error getting classification name for university {obj.id}: {e}")
                    if isinstance(obj.classification, (int, str)):
                        return f"Classification {obj.classification}"
                    return "Unknown Classification"
            except Exception as e:
                print(f"Unexpected error getting classification name for university {obj.id}: {e}")
                return "Classification Error"
        return None

    def to_representation(self, instance):
        """Convert the instance to a representation."""
        representation = super().to_representation(instance)

        # Handle logo field - don't return the file object itself
        if representation.get('logo') and not representation.get('logo_url'):
            representation['logo'] = None

        # Handle banner_image field - don't return the file object itself
        if representation.get('banner_image') and not representation.get('banner_image_url'):
            representation['banner_image'] = None

        # Ensure classification_name is populated
        if not representation.get('classification_name') and representation.get('classification_details') and representation['classification_details'].get('name'):
            representation['classification_name'] = representation['classification_details']['name']

        return representation

    def validate(self, data):
        """Validate the data."""
        # If admin_username is provided, admin_email and temporary_admin_password must also be provided
        if 'admin_username' in data and data['admin_username']:
            if not data.get('admin_email'):
                raise serializers.ValidationError({'admin_email': 'Admin email is required when providing an admin username.'})

            if not data.get('temporary_admin_password'):
                raise serializers.ValidationError({'temporary_admin_password': 'Admin password is required when providing an admin username.'})

        return data

    def create_or_update_admin_user(self, university, admin_username, admin_email, admin_password):
        """Helper method to create or update admin user for a university."""
        if not (admin_username and admin_email and admin_password):
            return False

        try:
            # Import required modules
            from django_tenants.utils import schema_context
            from django.contrib.auth.models import User, Group
            import logging

            logger = logging.getLogger(__name__)
            logger.info(f"Creating/updating admin user for university {university.name} (schema: {university.schema_name})")

            # Create the admin user in the tenant schema
            with schema_context(university.schema_name):
                # Check if user already exists
                if User.objects.filter(username=admin_username).exists():
                    # Update existing user
                    user = User.objects.get(username=admin_username)
                    user.email = admin_email
                    user.set_password(admin_password)
                    user.is_staff = True
                    user.is_superuser = True
                    user.first_name = "Admin"
                    user.last_name = university.name
                    user.save()
                    logger.info(f"Updated existing admin user for {university.name}: {user.username}")
                else:
                    # Create new admin user
                    user = User.objects.create_superuser(
                        username=admin_username,
                        email=admin_email,
                        password=admin_password,
                        first_name="Admin",
                        last_name=university.name
                    )
                    logger.info(f"Created new admin user for {university.name}: {user.username}")

                # Create or get University Admin group
                admin_group, _ = Group.objects.get_or_create(name="University Admin")

                # Add user to the group
                admin_group.user_set.add(user)

                # Mark admin as created
                university.admin_created = True
                university.save(update_fields=['admin_created'])

                logger.info(f"Admin user creation/update completed for {university.name}")
                return True

        except Exception as e:
            # Log the error but don't fail the university creation
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Error creating/updating admin user for university {university.name}: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            return False

    def create(self, validated_data):
        """Create a new university with admin user."""
        # Extract admin user data
        admin_username = validated_data.pop('admin_username', None)
        admin_email = validated_data.pop('admin_email', None)
        admin_password = validated_data.pop('temporary_admin_password', None)

        # Create the university
        university = University.objects.create(**validated_data)

        # Create admin user if credentials are provided
        self.create_or_update_admin_user(university, admin_username, admin_email, admin_password)

        return university

    def update(self, instance, validated_data):
        """Update a university and optionally create/update admin user."""
        # Extract admin user data
        admin_username = validated_data.pop('admin_username', None)
        admin_email = validated_data.pop('admin_email', None)
        admin_password = validated_data.pop('temporary_admin_password', None)

        # Update the university fields
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()

        # Create/update admin user if credentials are provided and admin not already created
        if not instance.admin_created or (admin_username and admin_email and admin_password):
            self.create_or_update_admin_user(instance, admin_username, admin_email, admin_password)

        return instance


class TagSerializer(serializers.ModelSerializer):
    """Serializer for Tag model."""

    class Meta:
        model = Tag
        fields = ['id', 'name', 'description', 'color', 'created_at', 'updated_at']


class AcademicYearSerializer(serializers.ModelSerializer):
    """Serializer for AcademicYear model."""

    class Meta:
        model = AcademicYear
        fields = ['id', 'year', 'is_current', 'description']

    def validate_year(self, value):
        """Validate that the year is in the correct format."""
        if not value:
            raise serializers.ValidationError("Year is required")

        # Check if the year is in the format YYYY-YYYY
        if not isinstance(value, str) or not value.strip():
            raise serializers.ValidationError("Year must be a non-empty string")

        if not value.strip().replace('-', '').isdigit():
            raise serializers.ValidationError("Year must contain only digits and a hyphen")

        if '-' not in value:
            raise serializers.ValidationError("Year must be in format YYYY-YYYY (e.g., 2023-2024)")

        parts = value.split('-')
        if len(parts) != 2 or len(parts[0]) != 4 or len(parts[1]) != 4:
            raise serializers.ValidationError("Year must be in format YYYY-YYYY (e.g., 2023-2024)")

        start_year = int(parts[0])
        end_year = int(parts[1])
        if end_year != start_year + 1:
            raise serializers.ValidationError("End year should be one year after start year")

        return value


class ThemeSerializer(serializers.ModelSerializer):
    """Serializer for Theme model."""
    tags = TagSerializer(many=True, read_only=True)

    class Meta:
        model = Theme
        fields = ['id', 'name', 'description', 'tags']


class SubThemeSerializer(serializers.ModelSerializer):
    """Serializer for SubTheme model."""
    theme = ThemeSerializer(read_only=True)
    theme_id = serializers.PrimaryKeyRelatedField(
        queryset=Theme.objects.all(),
        source='theme',
        write_only=True
    )
    tags = TagSerializer(many=True, read_only=True)

    class Meta:
        model = SubTheme
        fields = ['id', 'name', 'description', 'theme', 'theme_id', 'tags']


class MeasurementUnitSerializer(serializers.ModelSerializer):
    """Serializer for MeasurementUnit model."""
    kpi_count = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = MeasurementUnit
        fields = ['id', 'name', 'symbol', 'description', 'is_percentage', 'decimal_places', 'kpi_count']

    def get_kpi_count(self, obj):
        return obj.kpis.count()  # Use kpis to access the reverse relation (related_name in KPI model)


class KPISerializer(serializers.ModelSerializer):
    """Serializer for KPI model."""
    # Related objects (read-only)
    theme = ThemeSerializer(read_only=True)
    sub_theme = SubThemeSerializer(read_only=True)
    measurement_unit = MeasurementUnitSerializer(read_only=True)
    academic_year = AcademicYearSerializer(read_only=True)
    tags = TagSerializer(many=True, read_only=True)

    # Write-only fields for related objects
    theme_id = serializers.PrimaryKeyRelatedField(
        queryset=Theme.objects.all(),
        source='theme',
        write_only=True
    )
    sub_theme_id = serializers.PrimaryKeyRelatedField(
        queryset=SubTheme.objects.all(),
        source='sub_theme',
        write_only=True
    )
    measurement_unit_id = serializers.PrimaryKeyRelatedField(
        queryset=MeasurementUnit.objects.all(),
        source='measurement_unit',
        write_only=True
    )
    academic_year_id = serializers.PrimaryKeyRelatedField(
        queryset=AcademicYear.objects.all(),
        source='academic_year',
        write_only=True,
        required=False,
        allow_null=True
    )
    tag_ids = serializers.PrimaryKeyRelatedField(
        queryset=Tag.objects.all(),
        source='tags',
        write_only=True,
        many=True,
        required=False
    )

    # Computed fields
    current_value = serializers.SerializerMethodField()
    progress = serializers.SerializerMethodField()
    status_color = serializers.SerializerMethodField()
    on_track = serializers.SerializerMethodField()

    class Meta:
        model = KPI
        fields = [
            # Basic information
            'id', 'title', 'description', 'calculation_method',

            # Categorization
            'theme', 'theme_id', 'sub_theme', 'sub_theme_id', 'tags', 'tag_ids',

            # Measurement details
            'measurement_unit', 'measurement_unit_id', 'frequency', 'direction',
            'data_source', 'data_source_details',

            # Time-related fields
            'academic_year', 'academic_year_id',

            # Responsibility
            'responsible_department', 'responsible_person', 'contact_email',

            # Status and tracking
            'status', 'is_active', 'is_public', 'notes',

            # Computed fields
            'current_value', 'progress', 'status_color', 'on_track',

            # Audit fields
            'created_at', 'updated_at', 'created_by', 'last_updated_by'
        ]
        read_only_fields = ['created_at', 'updated_at', 'current_value', 'progress', 'status_color', 'on_track']

    def get_current_value(self, obj):
        """Get the current value of the KPI."""
        return obj.get_current_value()

    def get_progress(self, obj):
        """Get the progress towards the target as a percentage."""
        return obj.calculate_progress()

    def get_status_color(self, obj):
        """Get the color code for the KPI status."""
        return obj.get_status_display_color()

    def get_on_track(self, obj):
        """Determine if the KPI is on track to meet its target."""
        return obj.is_on_track()

    def validate(self, data):
        """Validate the KPI data."""
        # Validate that sub_theme belongs to the selected theme
        theme = data.get('theme')
        sub_theme = data.get('sub_theme')

        if theme and sub_theme and sub_theme.theme != theme:
            raise serializers.ValidationError({
                'sub_theme_id': 'Selected sub-theme must belong to the selected theme'
            })

        # Validate min/max values for range direction
        direction = data.get('direction')
        min_value = data.get('min_value')
        max_value = data.get('max_value')
        target_value = data.get('target_value')

        if direction == 'range':
            if min_value is None or max_value is None:
                raise serializers.ValidationError({
                    'direction': 'Min and max values must be set when direction is "Stay within range"'
                })
            if min_value >= max_value:
                raise serializers.ValidationError({
                    'min_value': 'Minimum value must be less than maximum value'
                })
            if target_value is not None and (target_value < min_value or target_value > max_value):
                raise serializers.ValidationError({
                    'target_value': 'Target value must be within the specified range'
                })

        return data

    def create(self, validated_data):
        """Create a new KPI with tags."""
        tags = validated_data.pop('tags', [])
        kpi = KPI.objects.create(**validated_data)
        if tags:
            kpi.tags.set(tags)
        return kpi

    def update(self, instance, validated_data):
        """Update a KPI with tags."""
        tags = validated_data.pop('tags', None)
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()

        if tags is not None:
            instance.tags.set(tags)

        return instance


class UniversityKPIAssignmentSerializer(serializers.ModelSerializer):
    """Serializer for UniversityKPIAssignment model."""
    university = UniversitySerializer(read_only=True)
    university_id = serializers.PrimaryKeyRelatedField(
        queryset=University.objects.all(),
        source='university',
        write_only=True
    )
    kpi = KPISerializer(read_only=True)
    kpi_id = serializers.PrimaryKeyRelatedField(
        queryset=KPI.objects.all(),
        source='kpi',
        write_only=True
    )
    academic_year = AcademicYearSerializer(read_only=True)
    academic_year_id = serializers.PrimaryKeyRelatedField(
        queryset=AcademicYear.objects.all(),
        source='academic_year',
        write_only=True,
        required=False
    )
    effective_target = serializers.DecimalField(
        max_digits=15, decimal_places=2,
        read_only=True
    )
    effective_baseline = serializers.DecimalField(
        max_digits=15, decimal_places=2,
        read_only=True
    )
    current_value = serializers.SerializerMethodField()
    progress = serializers.SerializerMethodField()
    latest_report_date = serializers.SerializerMethodField()
    total_weight = serializers.SerializerMethodField()
    kpi_title = serializers.SerializerMethodField()
    measurement_unit = serializers.SerializerMethodField()

    class Meta:
        model = UniversityKPIAssignment
        fields = [
            'id', 'university', 'university_id', 'kpi', 'kpi_id',
            'academic_year', 'academic_year_id', 'weight', 'target', 'min_value', 'max_value',
            'baseline_value', 'baseline_year', 'start_date', 'target_date',
            'notes', 'is_active', 'created_at', 'updated_at',
            'effective_target', 'effective_baseline',
            'current_value', 'progress', 'latest_report_date',
            'total_weight', 'kpi_title', 'measurement_unit'
        ]
        read_only_fields = ['created_at', 'updated_at', 'current_value', 'progress', 'latest_report_date', 'total_weight', 'kpi_title', 'measurement_unit']

    def get_current_value(self, obj):
        """Get the current value from the latest report."""
        return obj.get_current_value()

    def get_progress(self, obj):
        """Get the progress towards the target as a percentage."""
        return obj.calculate_progress()

    def get_latest_report_date(self, obj):
        """Get the date of the latest report."""
        latest_report = obj.get_latest_report()
        return latest_report.report_date if latest_report else None

    def get_total_weight(self, obj):
        """Get the total weight of all KPIs assigned to this university."""
        from django.db.models import Sum
        total = UniversityKPIAssignment.objects.filter(
            university=obj.university,
            academic_year=obj.academic_year,
            is_active=True
        ).aggregate(total=Sum('weight'))['total'] or 0
        return total

    def get_kpi_title(self, obj):
        """Get the title of the KPI."""
        return obj.kpi.title if obj.kpi else None

    def get_measurement_unit(self, obj):
        """Get the measurement unit of the KPI."""
        if obj.kpi and obj.kpi.measurement_unit:
            return {
                'name': obj.kpi.measurement_unit.name,
                'symbol': obj.kpi.measurement_unit.symbol
            }
        return None

    def validate(self, data):
        """Validate the weight and total weight of all university assignments."""
        weight = data.get('weight')
        if weight is not None and (weight < 0 or weight > 100):
            raise serializers.ValidationError({'weight': 'Weight must be between 0 and 100'})

        # Check if the total weight exceeds 100%
        university = data.get('university')
        academic_year = data.get('academic_year')

        if not university or not academic_year:
            return data

        # Get all existing assignments for this university and academic year
        from django.db.models import Sum
        existing_assignments = UniversityKPIAssignment.objects.filter(
            university=university,
            academic_year=academic_year,
            is_active=True
        )

        # If this is an update, exclude the current instance
        if self.instance:
            existing_assignments = existing_assignments.exclude(pk=self.instance.pk)

        # Calculate the total weight of existing assignments
        total_weight = existing_assignments.aggregate(total=Sum('weight'))['total'] or 0

        # Add the new weight
        new_total_weight = total_weight + weight

        if new_total_weight > 100:
            raise serializers.ValidationError({
                'weight': f'Total weight of all KPIs assigned to this university would exceed 100%. Current total: {total_weight}%, New weight: {weight}%, New total would be: {new_total_weight}%'
            })

        return data


class KPIReportSerializer(serializers.ModelSerializer):
    """Serializer for KPIReport model."""
    assignment = UniversityKPIAssignmentSerializer(read_only=True)
    assignment_id = serializers.PrimaryKeyRelatedField(
        queryset=UniversityKPIAssignment.objects.all(),
        source='assignment',
        write_only=True
    )
    academic_year = AcademicYearSerializer(read_only=True)
    academic_year_id = serializers.PrimaryKeyRelatedField(
        queryset=AcademicYear.objects.all(),
        source='academic_year',
        write_only=True,
        required=False,
        allow_null=True
    )
    kpi_title = serializers.SerializerMethodField()
    university_name = serializers.SerializerMethodField()
    measurement_unit = serializers.SerializerMethodField()

    class Meta:
        model = KPIReport
        fields = [
            'id', 'assignment', 'assignment_id', 'academic_year', 'academic_year_id',
            'report_date', 'value', 'status', 'notes',
            'evidence_url', 'evidence_file', 'reported_by', 'reported_at',
            'reviewed_by', 'reviewed_at', 'review_notes',
            'kpi_title', 'university_name', 'measurement_unit'
        ]
        read_only_fields = ['reported_at', 'reviewed_at', 'kpi_title', 'university_name', 'measurement_unit']

    def get_kpi_title(self, obj):
        """Get the title of the KPI."""
        return obj.assignment.kpi.title if obj.assignment and obj.assignment.kpi else None

    def get_university_name(self, obj):
        """Get the name of the university."""
        return obj.assignment.university.name if obj.assignment and obj.assignment.university else None

    def get_measurement_unit(self, obj):
        """Get the measurement unit of the KPI."""
        if obj.assignment and obj.assignment.kpi and obj.assignment.kpi.measurement_unit:
            return {
                'name': obj.assignment.kpi.measurement_unit.name,
                'symbol': obj.assignment.kpi.measurement_unit.symbol
            }
        return None


class OfficeSerializer(serializers.ModelSerializer):
    """Serializer for Office model."""
    manager = UserSerializer(read_only=True)
    manager_id = serializers.PrimaryKeyRelatedField(
        queryset=User.objects.all(),
        source='manager',
        write_only=True,
        required=False,
        allow_null=True
    )
    parent = serializers.SerializerMethodField()
    parent_id = serializers.PrimaryKeyRelatedField(
        queryset=Office.objects.all(),
        source='parent',
        write_only=True,
        required=False,
        allow_null=True
    )
    children_count = serializers.SerializerMethodField()
    positions_count = serializers.SerializerMethodField()
    kpi_assignments_count = serializers.SerializerMethodField()

    class Meta:
        model = Office
        fields = [
            'id', 'name', 'description', 'parent', 'parent_id', 'manager', 'manager_id',
            'email', 'phone', 'location', 'is_active', 'created_at', 'updated_at',
            'children_count', 'positions_count', 'kpi_assignments_count'
        ]
        read_only_fields = ['created_at', 'updated_at', 'children_count', 'positions_count', 'kpi_assignments_count']

    def get_parent(self, obj):
        """Get the parent office details."""
        if obj.parent:
            return {
                'id': obj.parent.id,
                'name': obj.parent.name
            }
        return None

    def get_children_count(self, obj):
        """Get the count of child offices."""
        return obj.children.count()

    def get_positions_count(self, obj):
        """Get the count of positions in this office."""
        return obj.positions.count()

    def get_kpi_assignments_count(self, obj):
        """Get the count of KPI assignments for this office."""
        # Check if the office has kpi_assignments attribute
        if hasattr(obj, 'kpi_assignments'):
            return obj.kpi_assignments.count()
        # Check if the office has kpi_assignments_new attribute (renamed attribute)
        elif hasattr(obj, 'kpi_assignments_new'):
            return obj.kpi_assignments_new.count()
        # Return 0 if neither attribute exists
        return 0

    def validate(self, data):
        """Validate that an office cannot be its own parent."""
        parent = data.get('parent')
        if self.instance and parent and parent.id == self.instance.id:
            raise serializers.ValidationError({'parent_id': 'An office cannot be its own parent.'})

        # Check for circular references
        if self.instance and parent:
            current_parent = parent
            while current_parent:
                if current_parent.parent and current_parent.parent.id == self.instance.id:
                    raise serializers.ValidationError({'parent_id': 'Circular reference detected in office hierarchy.'})
                current_parent = current_parent.parent

        return data


class UserPositionSerializer(serializers.ModelSerializer):
    """Serializer for UserPosition model."""
    user = UserSerializer(read_only=True)
    user_id = serializers.PrimaryKeyRelatedField(
        queryset=User.objects.all(),
        source='user',
        write_only=True
    )
    office = OfficeSerializer(read_only=True)
    office_id = serializers.PrimaryKeyRelatedField(
        queryset=Office.objects.all(),
        source='office',
        write_only=True
    )
    kpi_assignments_count = serializers.SerializerMethodField()

    class Meta:
        model = UserPosition
        fields = [
            'id', 'user', 'user_id', 'office', 'office_id', 'title',
            'is_manager', 'start_date', 'end_date', 'is_active',
            'created_at', 'updated_at', 'kpi_assignments_count'
        ]
        read_only_fields = ['created_at', 'updated_at', 'kpi_assignments_count']

    def get_kpi_assignments_count(self, obj):
        """Get the count of KPI assignments for this user."""
        return obj.user.kpi_assignments.count()

    def validate(self, data):
        """Validate start and end dates."""
        start_date = data.get('start_date')
        end_date = data.get('end_date')

        if start_date and end_date and end_date < start_date:
            raise serializers.ValidationError({'end_date': 'End date must be after start date.'})

        return data


class OfficeKPIAssignmentSerializer(serializers.ModelSerializer):
    """Serializer for OfficeKPIAssignment model."""
    university_assignment = UniversityKPIAssignmentSerializer(read_only=True)
    university_assignment_id = serializers.PrimaryKeyRelatedField(
        queryset=UniversityKPIAssignment.objects.all(),
        source='university_assignment',
        write_only=True
    )
    office = OfficeSerializer(read_only=True)
    office_id = serializers.PrimaryKeyRelatedField(
        queryset=Office.objects.all(),
        source='office',
        write_only=True
    )
    effective_target = serializers.DecimalField(
        max_digits=15, decimal_places=2,
        read_only=True
    )
    effective_baseline = serializers.DecimalField(
        max_digits=15, decimal_places=2,
        read_only=True
    )
    current_value = serializers.SerializerMethodField()
    progress = serializers.SerializerMethodField()
    latest_report_date = serializers.SerializerMethodField()
    user_assignments_count = serializers.SerializerMethodField()
    kpi_title = serializers.SerializerMethodField()
    measurement_unit = serializers.SerializerMethodField()

    class Meta:
        model = OfficeKPIAssignment
        fields = [
            'id', 'university_assignment', 'university_assignment_id', 'office', 'office_id',
            'weight', 'target', 'min_value', 'max_value', 'baseline_value', 'baseline_year',
            'start_date', 'target_date', 'notes', 'is_active',
            'effective_target', 'effective_baseline',
            'created_at', 'updated_at',
            'current_value', 'progress', 'latest_report_date',
            'user_assignments_count', 'kpi_title', 'measurement_unit'
        ]
        read_only_fields = ['created_at', 'updated_at', 'current_value', 'progress',
                          'latest_report_date', 'user_assignments_count', 'kpi_title', 'measurement_unit']

    def get_current_value(self, obj):
        """Get the current value from the latest report."""
        return obj.get_current_value()

    def get_progress(self, obj):
        """Get the progress towards the target as a percentage."""
        return obj.calculate_progress()

    def get_latest_report_date(self, obj):
        """Get the date of the latest report."""
        latest_report = obj.get_latest_report()
        return latest_report.report_date if latest_report else None

    def get_user_assignments_count(self, obj):
        """Get the count of user assignments for this office KPI assignment."""
        return obj.user_assignments.count()

    def get_kpi_title(self, obj):
        """Get the title of the KPI."""
        return obj.university_assignment.kpi.title if obj.university_assignment and obj.university_assignment.kpi else None

    def get_measurement_unit(self, obj):
        """Get the measurement unit of the KPI."""
        if obj.university_assignment and obj.university_assignment.kpi and obj.university_assignment.kpi.measurement_unit:
            return {
                'name': obj.university_assignment.kpi.measurement_unit.name,
                'symbol': obj.university_assignment.kpi.measurement_unit.symbol
            }
        return None

    def validate(self, data):
        """Validate the weight and total weight of all office assignments."""
        weight = data.get('weight')
        if weight is not None and (weight < 0 or weight > 100):
            raise serializers.ValidationError({'weight': 'Weight must be between 0 and 100'})

        university_assignment = data.get('university_assignment')
        if university_assignment and weight is not None:
            # Check total weight of all office assignments for this university assignment
            if self.instance:  # If this is an existing assignment
                total_weight = OfficeKPIAssignment.objects.filter(
                    university_assignment=university_assignment
                ).exclude(pk=self.instance.pk).aggregate(models.Sum('weight'))['weight__sum'] or 0
            else:  # If this is a new assignment
                total_weight = OfficeKPIAssignment.objects.filter(
                    university_assignment=university_assignment
                ).aggregate(models.Sum('weight'))['weight__sum'] or 0

            if total_weight + weight > 100:
                raise serializers.ValidationError({
                    'weight': f'Total weight of all office assignments for this KPI cannot exceed 100. Current total: {total_weight}, this assignment: {weight}'
                })

        return data


class UserKPIAssignmentSerializer(serializers.ModelSerializer):
    """Serializer for UserKPIAssignment model."""
    office_assignment = OfficeKPIAssignmentSerializer(read_only=True)
    office_assignment_id = serializers.PrimaryKeyRelatedField(
        queryset=OfficeKPIAssignment.objects.all(),
        source='office_assignment',
        write_only=True
    )
    user = UserSerializer(read_only=True)
    user_id = serializers.PrimaryKeyRelatedField(
        queryset=User.objects.all(),
        source='user',
        write_only=True
    )
    effective_target = serializers.DecimalField(
        max_digits=15, decimal_places=2,
        read_only=True
    )
    effective_baseline = serializers.DecimalField(
        max_digits=15, decimal_places=2,
        read_only=True
    )
    current_value = serializers.SerializerMethodField()
    progress = serializers.SerializerMethodField()
    latest_report_date = serializers.SerializerMethodField()
    kpi_title = serializers.SerializerMethodField()
    measurement_unit = serializers.SerializerMethodField()

    class Meta:
        model = UserKPIAssignment
        fields = [
            'id', 'office_assignment', 'office_assignment_id', 'user', 'user_id',
            'weight', 'target', 'min_value', 'max_value', 'baseline_value', 'baseline_year',
            'start_date', 'target_date', 'notes', 'is_active',
            'effective_target', 'effective_baseline',
            'created_at', 'updated_at',
            'current_value', 'progress', 'latest_report_date',
            'kpi_title', 'measurement_unit'
        ]
        read_only_fields = ['created_at', 'updated_at', 'current_value', 'progress',
                          'latest_report_date', 'kpi_title', 'measurement_unit']

    def get_current_value(self, obj):
        """Get the current value from the latest report."""
        return obj.get_current_value()

    def get_progress(self, obj):
        """Get the progress towards the target as a percentage."""
        return obj.calculate_progress()

    def get_latest_report_date(self, obj):
        """Get the date of the latest report."""
        latest_report = obj.get_latest_report()
        return latest_report.report_date if latest_report else None

    def get_kpi_title(self, obj):
        """Get the title of the KPI."""
        if obj.office_assignment and obj.office_assignment.university_assignment and obj.office_assignment.university_assignment.kpi:
            return obj.office_assignment.university_assignment.kpi.title
        return None

    def get_measurement_unit(self, obj):
        """Get the measurement unit of the KPI."""
        if (obj.office_assignment and obj.office_assignment.university_assignment and
            obj.office_assignment.university_assignment.kpi and obj.office_assignment.university_assignment.kpi.measurement_unit):
            return {
                'name': obj.office_assignment.university_assignment.kpi.measurement_unit.name,
                'symbol': obj.office_assignment.university_assignment.kpi.measurement_unit.symbol
            }
        return None

    def validate(self, data):
        """Validate the weight and total weight of all user assignments."""
        weight = data.get('weight')
        if weight is not None and (weight < 0 or weight > 100):
            raise serializers.ValidationError({'weight': 'Weight must be between 0 and 100'})

        office_assignment = data.get('office_assignment')
        user = data.get('user')
        if office_assignment and weight is not None:
            # Check total weight of all user assignments for this office assignment
            if self.instance:  # If this is an existing assignment
                total_weight = UserKPIAssignment.objects.filter(
                    office_assignment=office_assignment
                ).exclude(pk=self.instance.pk).aggregate(models.Sum('weight'))['weight__sum'] or 0
            else:  # If this is a new assignment
                total_weight = UserKPIAssignment.objects.filter(
                    office_assignment=office_assignment
                ).aggregate(models.Sum('weight'))['weight__sum'] or 0

            if total_weight + weight > 100:
                raise serializers.ValidationError({
                    'weight': f'Total weight of all user assignments for this KPI cannot exceed 100. Current total: {total_weight}, this assignment: {weight}'
                })

        # Validate that the user belongs to the office
        if user and office_assignment:
            try:
                user_position = UserPosition.objects.get(user=user)
                if user_position.office != office_assignment.office:
                    raise serializers.ValidationError({'user_id': 'User must belong to the assigned office'})
            except UserPosition.DoesNotExist:
                raise serializers.ValidationError({'user_id': 'User must have a position in an office'})

        return data


class OfficeKPIReportSerializer(serializers.ModelSerializer):
    """Serializer for OfficeKPIReport model."""
    assignment = OfficeKPIAssignmentSerializer(read_only=True)
    assignment_id = serializers.PrimaryKeyRelatedField(
        queryset=OfficeKPIAssignment.objects.all(),
        source='assignment',
        write_only=True
    )
    academic_year = AcademicYearSerializer(read_only=True)
    academic_year_id = serializers.PrimaryKeyRelatedField(
        queryset=AcademicYear.objects.all(),
        source='academic_year',
        write_only=True,
        required=False,
        allow_null=True
    )
    kpi_title = serializers.SerializerMethodField()
    office_name = serializers.SerializerMethodField()
    measurement_unit = serializers.SerializerMethodField()

    class Meta:
        model = OfficeKPIReport
        fields = [
            'id', 'assignment', 'assignment_id', 'academic_year', 'academic_year_id',
            'report_date', 'value', 'status', 'notes',
            'evidence_url', 'evidence_file', 'reported_by', 'reported_at',
            'reviewed_by', 'reviewed_at', 'review_notes',
            'kpi_title', 'office_name', 'measurement_unit'
        ]
        read_only_fields = ['reported_at', 'reviewed_at', 'kpi_title', 'office_name', 'measurement_unit']

    def get_kpi_title(self, obj):
        """Get the title of the KPI."""
        if obj.assignment and obj.assignment.university_assignment and obj.assignment.university_assignment.kpi:
            return obj.assignment.university_assignment.kpi.title
        return None

    def get_office_name(self, obj):
        """Get the name of the office."""
        return obj.assignment.office.name if obj.assignment and obj.assignment.office else None

    def get_measurement_unit(self, obj):
        """Get the measurement unit of the KPI."""
        if (obj.assignment and obj.assignment.university_assignment and
            obj.assignment.university_assignment.kpi and obj.assignment.university_assignment.kpi.measurement_unit):
            return {
                'name': obj.assignment.university_assignment.kpi.measurement_unit.name,
                'symbol': obj.assignment.university_assignment.kpi.measurement_unit.symbol
            }
        return None


class UserKPIReportSerializer(serializers.ModelSerializer):
    """Serializer for UserKPIReport model."""
    assignment = UserKPIAssignmentSerializer(read_only=True)
    assignment_id = serializers.PrimaryKeyRelatedField(
        queryset=UserKPIAssignment.objects.all(),
        source='assignment',
        write_only=True
    )
    academic_year = AcademicYearSerializer(read_only=True)
    academic_year_id = serializers.PrimaryKeyRelatedField(
        queryset=AcademicYear.objects.all(),
        source='academic_year',
        write_only=True,
        required=False,
        allow_null=True
    )
    kpi_title = serializers.SerializerMethodField()
    user_name = serializers.SerializerMethodField()
    office_name = serializers.SerializerMethodField()
    measurement_unit = serializers.SerializerMethodField()

    class Meta:
        model = UserKPIReport
        fields = [
            'id', 'assignment', 'assignment_id', 'academic_year', 'academic_year_id',
            'report_date', 'value', 'status', 'notes',
            'evidence_url', 'evidence_file', 'reported_by', 'reported_at',
            'reviewed_by', 'reviewed_at', 'review_notes',
            'kpi_title', 'user_name', 'office_name', 'measurement_unit'
        ]
        read_only_fields = ['reported_at', 'reviewed_at', 'kpi_title', 'user_name', 'office_name', 'measurement_unit']

    def get_kpi_title(self, obj):
        """Get the title of the KPI."""
        if (obj.assignment and obj.assignment.office_assignment and
            obj.assignment.office_assignment.university_assignment and
            obj.assignment.office_assignment.university_assignment.kpi):
            return obj.assignment.office_assignment.university_assignment.kpi.title
        return None

    def get_user_name(self, obj):
        """Get the name of the user."""
        return obj.assignment.user.get_full_name() if obj.assignment and obj.assignment.user else None

    def get_office_name(self, obj):
        """Get the name of the office."""
        if obj.assignment and obj.assignment.office_assignment and obj.assignment.office_assignment.office:
            return obj.assignment.office_assignment.office.name
        return None

    def get_measurement_unit(self, obj):
        """Get the measurement unit of the KPI."""
        if (obj.assignment and obj.assignment.office_assignment and
            obj.assignment.office_assignment.university_assignment and
            obj.assignment.office_assignment.university_assignment.kpi and
            obj.assignment.office_assignment.university_assignment.kpi.measurement_unit):
            return {
                'name': obj.assignment.office_assignment.university_assignment.kpi.measurement_unit.name,
                'symbol': obj.assignment.office_assignment.university_assignment.kpi.measurement_unit.symbol
            }
        return None


class AuditLogSerializer(serializers.ModelSerializer):
    """Serializer for AuditLog model."""
    user = UserSerializer(read_only=True)

    class Meta:
        model = AuditLog
        fields = [
            'id', 'user', 'action', 'content_type', 'object_id',
            'description', 'timestamp', 'ip_address'
        ]
        read_only_fields = ['id', 'user', 'action', 'content_type', 'object_id', 'description', 'timestamp', 'ip_address']
