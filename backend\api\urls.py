from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from drf_yasg.views import get_schema_view
from drf_yasg import openapi
from rest_framework import permissions
from rest_framework_simplejwt.views import (
    TokenVerifyView,
    TokenBlacklistView,
)
from .token_views import TenantAwareTokenObtainPairView, TenantAwareTokenRefreshView, LogoutView, ClearCookiesView

# Import import/export functions
from .imports_users import import_users, download_user_template
from .imports_offices import import_offices, download_office_template
from .exports_users import export_users_excel, export_users_pdf
from .exports_offices import export_offices_excel, export_offices_pdf

# Import all views explicitly to avoid AttributeError
from .views import (
    UserViewSet, UniversityViewSet, DomainViewSet, TagViewSet, UniversityClassificationViewSet,
    AcademicYearViewSet, ThemeViewSet, SubThemeViewSet, MeasurementUnitViewSet,
    KPIViewSet, UniversityKPIAssignmentViewSet, KPIReportViewSet,
    OfficeViewSet, UserPositionViewSet, OfficeKPIAssignmentViewSet,
    UserKPIAssignmentViewSet, OfficeKPIReportViewSet, UserKPIReportViewSet,
    AuditLogViewSet, ReportTemplateViewSet, current_user, moe_dashboard, university_dashboard,
    current_tenant, api_documentation, kpi_performance
)

# Import CSRF view
from .views.csrf import get_csrf_token

# Import aggregation views
from .views.aggregation import (
    aggregate_user_reports, aggregate_office_reports,
    aggregate_university_reports, update_aggregations
)

# Import analytics dashboard views
from .views.analytics_dashboard import (
    performance_dashboard, benchmarking_data, strategic_insights, accountability_report
)

# Create a router and register our viewsets with it
router = DefaultRouter()
router.register(r'users', UserViewSet)
router.register(r'universities', UniversityViewSet)
router.register(r'domains', DomainViewSet)
router.register(r'university-classifications', UniversityClassificationViewSet)
router.register(r'tags', TagViewSet)
router.register(r'academic-years', AcademicYearViewSet)
router.register(r'themes', ThemeViewSet)
router.register(r'subthemes', SubThemeViewSet)
router.register(r'measurement-units', MeasurementUnitViewSet)
router.register(r'kpis', KPIViewSet)
router.register(r'kpi-assignments', UniversityKPIAssignmentViewSet)
# Register the same viewset with a different URL for frontend compatibility
router.register(r'assignments', UniversityKPIAssignmentViewSet)
router.register(r'kpi-reports', KPIReportViewSet)
# Register the same viewset with a different URL for frontend compatibility
router.register(r'reports', KPIReportViewSet)

# Register new viewsets for offices and KPI assignments
router.register(r'offices', OfficeViewSet, basename='office')
router.register(r'positions', UserPositionViewSet, basename='position')
router.register(r'office-kpi-assignments', OfficeKPIAssignmentViewSet, basename='office-kpi-assignment')
router.register(r'user-kpi-assignments', UserKPIAssignmentViewSet)
router.register(r'office-kpi-reports', OfficeKPIReportViewSet)
router.register(r'user-kpi-reports', UserKPIReportViewSet)
router.register(r'audit-logs', AuditLogViewSet)
router.register(r'report-templates', ReportTemplateViewSet)

# Schema view for Swagger/OpenAPI documentation
schema_view = get_schema_view(
    openapi.Info(
        title="MoE KPI Tracker API",
        default_version='v1',
        description="API for the Ministry of Education KPI Tracker system",
        terms_of_service="https://www.moe.gov/terms/",
        contact=openapi.Contact(email="<EMAIL>"),
        license=openapi.License(name="MIT License"),
    ),
    public=True,
    permission_classes=[permissions.AllowAny],  # Allow unauthenticated access to the schema
)

app_name = 'api'

urlpatterns = [
    # API endpoints
    path('', include(router.urls)),

    # API authentication
    path('auth/', include('rest_framework.urls', namespace='rest_framework')),

    # JWT Token endpoints
    path('auth/token/', TenantAwareTokenObtainPairView.as_view(), name='token_obtain_pair'),
    path('auth/token/refresh/', TenantAwareTokenRefreshView.as_view(), name='token_refresh'),
    path('auth/token/verify/', TokenVerifyView.as_view(), name='token_verify'),
    path('auth/token/blacklist/', TokenBlacklistView.as_view(), name='token_blacklist'),
    path('auth/logout/', LogoutView.as_view(), name='logout'),
    path('auth/clear-cookies/', ClearCookiesView.as_view(), name='clear_cookies'),

    # User endpoint
    path('auth/user/', current_user, name='current_user'),

    # Dashboard endpoints
    path('dashboard/moe/', moe_dashboard, name='moe_dashboard'),
    path('dashboard/university/', university_dashboard, name='university_dashboard'),

    # Tenant information endpoint
    path('tenant/', current_tenant, name='current_tenant'),

    # CSRF token endpoint
    path('csrf/', get_csrf_token, name='csrf_token'),

    # Template download endpoints
    path('kpis/download_template/', KPIViewSet.as_view({'get': 'download_template'}), name='kpi-download-template'),
    path('themes/download_template/', ThemeViewSet.as_view({'get': 'download_template'}), name='theme-download-template'),
    path('subthemes/download_template/', SubThemeViewSet.as_view({'get': 'download_template'}), name='subtheme-download-template'),
    path('measurement-units/download_template/', MeasurementUnitViewSet.as_view({'get': 'download_template'}), name='measurement-unit-download-template'),

    # User import/export endpoints
    path('users/download_template/', download_user_template, name='user-download-template'),
    path('users/import_users/', import_users, name='user-import'),
    path('users/export_excel/', export_users_excel, name='user-export-excel'),
    path('users/export_pdf/', export_users_pdf, name='user-export-pdf'),

    # Office import/export endpoints
    path('offices/download_template/', download_office_template, name='office-download-template'),
    path('offices/import_offices/', import_offices, name='office-import'),
    path('offices/export_excel/', export_offices_excel, name='office-export-excel'),
    path('offices/export_pdf/', export_offices_pdf, name='office-export-pdf'),

    # API documentation
    path('docs/', schema_view.with_ui('swagger', cache_timeout=0), name='schema-swagger-ui'),
    path('redoc/', schema_view.with_ui('redoc', cache_timeout=0), name='schema-redoc'),
    path('documentation/', api_documentation, name='documentation'),
    path('schema/', schema_view.without_ui(cache_timeout=0), name='schema-json'),

    # System settings endpoints
    path('settings/', include('system_settings.urls')),

    # Aggregation endpoints
    path('aggregation/user-reports/<int:office_assignment_id>/', aggregate_user_reports, name='aggregate_user_reports'),
    path('aggregation/office-reports/<int:university_assignment_id>/', aggregate_office_reports, name='aggregate_office_reports'),
    path('aggregation/university-reports/', aggregate_university_reports, name='aggregate_university_reports'),
    path('aggregation/update/', update_aggregations, name='update_aggregations'),

    # KPI Performance endpoints
    path('kpi-performance/', kpi_performance, name='kpi_performance'),
    path('kpi-performance/<int:kpi_id>/', kpi_performance, name='kpi_performance_detail'),
    path('kpis/<int:kpi_id>/performance/', kpi_performance, name='kpi_performance_by_kpi'),

    # Analytics Dashboard endpoints
    path('analytics/performance-dashboard/', performance_dashboard, name='performance_dashboard'),
    path('analytics/benchmarking-data/', benchmarking_data, name='benchmarking_data'),
    path('analytics/strategic-insights/', strategic_insights, name='strategic_insights'),
]
