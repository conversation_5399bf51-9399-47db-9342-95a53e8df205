"""
<PERSON><PERSON>t to create seed users, offices, and their KPI assignments.
This script ensures each university has basic users and offices with KPI assignments.
"""

import os
import sys
import django
import logging
import random
from decimal import Decimal
from datetime import date

# Add the project directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'moe_kpi_tracker.settings')
django.setup()

# Import after Django setup
from django.contrib.auth.models import User
from django.db import transaction
from django_tenants.utils import schema_context
from universities.models import University
from offices.models import Office, UserProfile
from kpi_definitions.models import KPI, AcademicYear
from kpi_assignments.models import UniversityKPIAssignment, OfficeKPIAssignment, UserKPIAssignment

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Sample office data
SAMPLE_OFFICES = [
    {
        "name": "Academic Affairs Office",
        "description": "Manages academic programs and curriculum",
        "users": [
            {"username": "academic.director", "first_name": "Academic", "last_name": "Director", "position": "Director", "is_manager": True},
            {"username": "curriculum.coordinator", "first_name": "Curriculum", "last_name": "Coordinator", "position": "Coordinator", "is_manager": False},
        ]
    },
    {
        "name": "Student Affairs Office", 
        "description": "Handles student services and support",
        "users": [
            {"username": "student.director", "first_name": "Student", "last_name": "Director", "position": "Director", "is_manager": True},
            {"username": "student.counselor", "first_name": "Student", "last_name": "Counselor", "position": "Counselor", "is_manager": False},
        ]
    },
    {
        "name": "Research Office",
        "description": "Coordinates research activities",
        "users": [
            {"username": "research.director", "first_name": "Research", "last_name": "Director", "position": "Director", "is_manager": True},
            {"username": "research.coordinator", "first_name": "Research", "last_name": "Coordinator", "position": "Coordinator", "is_manager": False},
        ]
    },
    {
        "name": "Finance Office",
        "description": "Manages financial resources and budgeting",
        "users": [
            {"username": "finance.director", "first_name": "Finance", "last_name": "Director", "position": "Director", "is_manager": True},
            {"username": "budget.officer", "first_name": "Budget", "last_name": "Officer", "position": "Officer", "is_manager": False},
        ]
    },
    {
        "name": "Human Resources Office",
        "description": "Manages human resources and personnel",
        "users": [
            {"username": "hr.director", "first_name": "HR", "last_name": "Director", "position": "Director", "is_manager": True},
            {"username": "hr.officer", "first_name": "HR", "last_name": "Officer", "position": "Officer", "is_manager": False},
        ]
    }
]

def get_or_create_academic_year():
    """Get or create the current academic year."""
    try:
        academic_year = AcademicYear.objects.filter(is_current=True).first()
        if academic_year:
            return academic_year
    except:
        pass
    
    # Create a default academic year
    current_year = date.today().year
    academic_year = AcademicYear.objects.create(
        name=f"{current_year}-{current_year + 1}",
        start_date=date(current_year, 9, 1),
        end_date=date(current_year + 1, 8, 31),
        is_current=True
    )
    logger.info(f"Created academic year: {academic_year.name}")
    return academic_year

def create_seed_offices_and_users(university):
    """Create seed offices and users for a university."""
    logger.info(f"Creating seed offices and users for {university.name}")
    
    offices_created = 0
    users_created = 0
    
    with schema_context(university.schema_name):
        for office_data in SAMPLE_OFFICES:
            # Create or get office
            office, created = Office.objects.get_or_create(
                name=office_data["name"],
                defaults={
                    "description": office_data["description"],
                    "is_active": True
                }
            )
            
            if created:
                offices_created += 1
                logger.info(f"Created office: {office.name}")
            
            # Create users for this office
            for user_data in office_data["users"]:
                # Create unique username for this university
                username = f"{user_data['username']}.{university.schema_name}"
                email = f"{user_data['username']}@{university.schema_name}.edu.et"
                
                # Create or get user
                user, user_created = User.objects.get_or_create(
                    username=username,
                    defaults={
                        "email": email,
                        "first_name": user_data["first_name"],
                        "last_name": user_data["last_name"],
                        "password": "pbkdf2_sha256$600000$dummy$dummy",  # Dummy password hash
                        "is_active": True,
                        "is_staff": user_data.get("is_manager", False)
                    }
                )
                
                if user_created:
                    users_created += 1
                    logger.info(f"Created user: {user.username}")
                
                # Create or update user profile
                profile, profile_created = UserProfile.objects.get_or_create(
                    user=user,
                    defaults={
                        "office": office,
                        "position": user_data["position"],
                        "is_manager": user_data.get("is_manager", False),
                        "is_active": True
                    }
                )
                
                if not profile_created:
                    profile.office = office
                    profile.position = user_data["position"]
                    profile.is_manager = user_data.get("is_manager", False)
                    profile.save()
                
                # Set office manager
                if user_data.get("is_manager", False) and not office.manager:
                    office.manager = user
                    office.save()
    
    return offices_created, users_created

def create_office_kpi_assignments(university, academic_year):
    """Create KPI assignments for offices."""
    logger.info(f"Creating office KPI assignments for {university.name}")
    
    assignments_created = 0
    
    with schema_context(university.schema_name):
        # Get university KPI assignments
        university_assignments = UniversityKPIAssignment.objects.filter(
            university=university,
            academic_year=academic_year,
            is_active=True
        )
        
        # Get all offices
        offices = Office.objects.filter(is_active=True)
        
        for university_assignment in university_assignments:
            # Assign to 2-3 random offices per KPI
            selected_offices = random.sample(list(offices), min(3, len(offices)))
            
            for office in selected_offices:
                # Check if assignment already exists
                if OfficeKPIAssignment.objects.filter(
                    university_assignment=university_assignment,
                    office=office
                ).exists():
                    continue
                
                # Create office assignment with realistic values
                target = university_assignment.target or Decimal('100.0')
                weight = Decimal(str(random.uniform(15.0, 35.0)))  # 15-35% weight
                
                OfficeKPIAssignment.objects.create(
                    university_assignment=university_assignment,
                    office=office,
                    weight=weight,
                    target=target,
                    min_value=target * Decimal('0.8'),
                    max_value=target * Decimal('1.2'),
                    baseline_value=target * Decimal('0.7'),
                    baseline_year=str(date.today().year - 1),
                    start_date=academic_year.start_date,
                    target_date=academic_year.end_date,
                    notes=f"Seed assignment for {office.name}",
                    is_active=True
                )
                
                assignments_created += 1
                logger.info(f"Created office assignment: {office.name} -> {university_assignment.kpi.title}")
    
    return assignments_created

def create_user_kpi_assignments(university, academic_year):
    """Create KPI assignments for users."""
    logger.info(f"Creating user KPI assignments for {university.name}")
    
    assignments_created = 0
    
    with schema_context(university.schema_name):
        # Get office KPI assignments
        office_assignments = OfficeKPIAssignment.objects.filter(
            university_assignment__academic_year=academic_year,
            is_active=True
        )
        
        for office_assignment in office_assignments:
            # Get users in this office
            users = User.objects.filter(
                profile__office=office_assignment.office,
                profile__is_active=True
            )
            
            if not users.exists():
                continue
            
            # Assign to all users in the office
            total_weight = Decimal('100.0')
            user_count = users.count()
            base_weight = total_weight / user_count
            
            for user in users:
                # Check if assignment already exists
                if UserKPIAssignment.objects.filter(
                    office_assignment=office_assignment,
                    user=user
                ).exists():
                    continue
                
                # Managers get slightly higher weight
                is_manager = hasattr(user, 'profile') and user.profile.is_manager
                weight = base_weight * Decimal('1.2') if is_manager else base_weight
                
                # Calculate user target
                office_target = office_assignment.target or Decimal('100.0')
                user_target = office_target * (weight / Decimal('100.0'))
                
                UserKPIAssignment.objects.create(
                    office_assignment=office_assignment,
                    user=user,
                    weight=weight,
                    target=user_target,
                    min_value=user_target * Decimal('0.8'),
                    max_value=user_target * Decimal('1.2'),
                    baseline_value=user_target * Decimal('0.7'),
                    baseline_year=str(date.today().year - 1),
                    start_date=academic_year.start_date,
                    target_date=academic_year.end_date,
                    notes=f"Seed assignment for {user.get_full_name() or user.username}",
                    is_active=True
                )
                
                assignments_created += 1
                logger.info(f"Created user assignment: {user.username} -> {office_assignment.university_assignment.kpi.title}")
    
    return assignments_created

def seed_university(university, academic_year):
    """Seed a university with users, offices, and KPI assignments."""
    logger.info(f"Seeding university: {university.name}")
    
    try:
        with transaction.atomic():
            # Create offices and users
            offices_created, users_created = create_seed_offices_and_users(university)
            
            # Create KPI assignments
            office_assignments = create_office_kpi_assignments(university, academic_year)
            user_assignments = create_user_kpi_assignments(university, academic_year)
            
            logger.info(f"Seeded {university.name}: {offices_created} offices, {users_created} users, "
                       f"{office_assignments} office assignments, {user_assignments} user assignments")
            
            return offices_created, users_created, office_assignments, user_assignments
    
    except Exception as e:
        logger.error(f"Error seeding {university.name}: {str(e)}")
        return 0, 0, 0, 0

def main():
    """Main function to seed all universities."""
    logger.info("Starting seed user and office KPI assignments process...")
    
    # Get or create academic year
    academic_year = get_or_create_academic_year()
    
    # Get all universities except public tenant
    universities = University.objects.exclude(schema_name='public')
    
    total_offices = 0
    total_users = 0
    total_office_assignments = 0
    total_user_assignments = 0
    
    for university in universities:
        offices, users, office_assignments, user_assignments = seed_university(university, academic_year)
        total_offices += offices
        total_users += users
        total_office_assignments += office_assignments
        total_user_assignments += user_assignments
    
    logger.info("Seeding completed!")
    logger.info(f"Total created: {total_offices} offices, {total_users} users, "
               f"{total_office_assignments} office assignments, {total_user_assignments} user assignments")

if __name__ == "__main__":
    main()
