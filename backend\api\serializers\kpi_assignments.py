from rest_framework import serializers
from django.utils import timezone
from django.db.models import Sum
from kpi_assignments.models import (
    UniversityKPIAssignment, OfficeKPIAssignment, UserKPIAssignment,
    KPIReport, OfficeKPIReport, UserKPIReport
)
from kpi_definitions.models import KPI, AcademicYear
from universities.models import University
from offices.models import Office
from django.contrib.auth.models import User


class UniversityKPIAssignmentSerializer(serializers.ModelSerializer):
    university_name = serializers.StringRelatedField(source='university.name', read_only=True)
    kpi_title = serializers.StringRelatedField(source='kpi.title', read_only=True)
    academic_year_name = serializers.StringRelatedField(source='academic_year.name', read_only=True)
    measurement_unit_symbol = serializers.StringRelatedField(source='kpi.measurement_unit.symbol', read_only=True)
    current_value = serializers.DecimalField(max_digits=15, decimal_places=2, read_only=True)
    progress = serializers.SerializerMethodField(read_only=True)
    status_color = serializers.CharField(read_only=True)
    on_track = serializers.BooleanField(read_only=True)
    kpi_direction = serializers.CharField(source='kpi.direction', read_only=True)

    # Add theme and subtheme names
    theme_name = serializers.StringRelatedField(source='kpi.theme.name', read_only=True)
    sub_theme_name = serializers.StringRelatedField(source='kpi.sub_theme.name', read_only=True)
    kpi_theme_name = serializers.StringRelatedField(source='kpi.theme.name', read_only=True)
    kpi_sub_theme_name = serializers.StringRelatedField(source='kpi.sub_theme.name', read_only=True)

    # Add weight distribution status
    total_office_weights = serializers.DecimalField(max_digits=5, decimal_places=2, read_only=True)
    weight_distribution_complete = serializers.BooleanField(read_only=True)
    weight_distribution_status = serializers.SerializerMethodField(read_only=True)

    def get_weight_distribution_status(self, obj):
        return obj.weight_distribution_status

    class Meta:
        model = UniversityKPIAssignment
        fields = [
            'id', 'university', 'university_name', 'kpi', 'kpi_title',
            'academic_year', 'academic_year_name', 'weight', 'target', 'min_value', 'max_value',
            'baseline_value', 'baseline_year', 'start_date', 'target_date',
            'notes', 'is_active', 'created_at', 'updated_at', 'measurement_unit_symbol',
            'current_value', 'progress', 'status_color', 'on_track', 'kpi_direction',
            'theme_name', 'sub_theme_name', 'kpi_theme_name', 'kpi_sub_theme_name',
            'total_office_weights', 'weight_distribution_complete', 'weight_distribution_status'
        ]
        read_only_fields = ['created_at', 'updated_at', 'current_value', 'progress', 'status_color', 'on_track',
                           'theme_name', 'sub_theme_name', 'kpi_theme_name', 'kpi_sub_theme_name',
                           'total_office_weights', 'weight_distribution_complete', 'weight_distribution_status']

    def get_progress(self, obj):
        return obj.calculate_progress()

    def validate(self, data):
        """
        Validate the assignment data.
        """
        # Handle empty strings for date fields
        if 'start_date' in data and data['start_date'] == '':
            data['start_date'] = None

        if 'target_date' in data and data['target_date'] == '':
            data['target_date'] = None

        if 'baseline_year' in data and data['baseline_year'] == '':
            data['baseline_year'] = None

        # Validate weight
        if 'weight' in data:
            weight = data['weight']
            if weight < 0 or weight > 100:
                raise serializers.ValidationError({'weight': 'Weight must be between 0 and 100'})

        return data


class OfficeKPIAssignmentSerializer(serializers.ModelSerializer):
    office_name = serializers.StringRelatedField(source='office.name', read_only=True)
    university_assignment_id = serializers.PrimaryKeyRelatedField(source='university_assignment', read_only=True)
    kpi_title = serializers.StringRelatedField(source='university_assignment.kpi.title', read_only=True)
    academic_year_name = serializers.StringRelatedField(source='university_assignment.academic_year.name', read_only=True)
    measurement_unit_symbol = serializers.StringRelatedField(source='university_assignment.kpi.measurement_unit.symbol', read_only=True)
    university_target = serializers.DecimalField(source='university_assignment.target', max_digits=15, decimal_places=2, read_only=True)
    university_min_value = serializers.DecimalField(source='university_assignment.min_value', max_digits=15, decimal_places=2, read_only=True)
    university_max_value = serializers.DecimalField(source='university_assignment.max_value', max_digits=15, decimal_places=2, read_only=True)
    university_baseline_value = serializers.DecimalField(source='university_assignment.baseline_value', max_digits=15, decimal_places=2, read_only=True)
    university_baseline_year = serializers.CharField(source='university_assignment.baseline_year', read_only=True)
    university_start_date = serializers.DateField(source='university_assignment.start_date', read_only=True)
    university_target_date = serializers.DateField(source='university_assignment.target_date', read_only=True)
    total_user_weights = serializers.DecimalField(max_digits=5, decimal_places=2, read_only=True)
    weight_distribution_complete = serializers.BooleanField(read_only=True)
    weight_distribution_status = serializers.SerializerMethodField(read_only=True)
    current_value = serializers.DecimalField(max_digits=15, decimal_places=2, read_only=True)
    progress = serializers.SerializerMethodField(read_only=True)
    status_color = serializers.CharField(read_only=True)
    on_track = serializers.BooleanField(read_only=True)
    kpi_direction = serializers.CharField(source='university_assignment.kpi.direction', read_only=True)

    def get_weight_distribution_status(self, obj):
        return obj.weight_distribution_status

    # Add theme and subtheme names
    theme_name = serializers.StringRelatedField(source='university_assignment.kpi.theme.name', read_only=True)
    sub_theme_name = serializers.StringRelatedField(source='university_assignment.kpi.sub_theme.name', read_only=True)
    kpi_theme_name = serializers.StringRelatedField(source='university_assignment.kpi.theme.name', read_only=True)
    kpi_sub_theme_name = serializers.StringRelatedField(source='university_assignment.kpi.sub_theme.name', read_only=True)

    class Meta:
        model = OfficeKPIAssignment
        fields = [
            'id', 'university_assignment', 'university_assignment_id', 'office', 'office_name',
            'kpi_title', 'academic_year_name', 'weight', 'target', 'min_value', 'max_value',
            'baseline_value', 'baseline_year', 'start_date', 'target_date',
            'university_target', 'university_min_value', 'university_max_value',
            'university_baseline_value', 'university_baseline_year',
            'university_start_date', 'university_target_date',
            'notes', 'is_active', 'created_at', 'updated_at', 'measurement_unit_symbol',
            'total_user_weights', 'weight_distribution_complete', 'weight_distribution_status',
            'current_value', 'progress', 'status_color', 'on_track', 'kpi_direction',
            'theme_name', 'sub_theme_name', 'kpi_theme_name', 'kpi_sub_theme_name'
        ]
        read_only_fields = ['created_at', 'updated_at', 'total_user_weights', 'weight_distribution_complete',
                           'weight_distribution_status', 'current_value', 'progress', 'status_color', 'on_track',
                           'theme_name', 'sub_theme_name', 'kpi_theme_name', 'kpi_sub_theme_name']

    def get_progress(self, obj):
        return obj.calculate_progress()

    def validate(self, data):
        """
        Validate the assignment data.
        """
        # Handle empty strings for date fields
        if 'start_date' in data and data['start_date'] == '':
            data['start_date'] = None

        if 'target_date' in data and data['target_date'] == '':
            data['target_date'] = None

        if 'baseline_year' in data and data['baseline_year'] == '':
            data['baseline_year'] = None

        # Validate weight
        if 'weight' in data:
            weight = data['weight']
            if weight < 0 or weight > 100:
                raise serializers.ValidationError({'weight': 'Weight must be between 0 and 100'})

            # Check if this is a new assignment or an update
            university_assignment = None
            if self.instance:
                # This is an update
                university_assignment = self.instance.university_assignment
            elif 'university_assignment' in data:
                # This is a new assignment
                university_assignment = data['university_assignment']

            if university_assignment:
                # Get all existing office assignments for this university assignment
                existing_assignments = OfficeKPIAssignment.objects.filter(
                    university_assignment=university_assignment,
                    is_active=True
                )

                # If this is an update, exclude the current instance
                if self.instance:
                    existing_assignments = existing_assignments.exclude(pk=self.instance.pk)

                # Calculate the total weight of existing assignments
                total_weight = existing_assignments.aggregate(Sum('weight'))['weight__sum'] or 0

                # Add the new weight
                new_total_weight = total_weight + weight

                if new_total_weight > 100:
                    raise serializers.ValidationError({
                        'weight': f'Total weight of all office assignments for this KPI would exceed 100%. Current total: {total_weight}%, New weight: {weight}%, New total would be: {new_total_weight}%'
                    })

        return data


class UserKPIAssignmentSerializer(serializers.ModelSerializer):
    user_username = serializers.StringRelatedField(source='user.username', read_only=True)
    user_email = serializers.StringRelatedField(source='user.email', read_only=True)
    user_full_name = serializers.SerializerMethodField(read_only=True)
    office_assignment_id = serializers.PrimaryKeyRelatedField(source='office_assignment', read_only=True)
    office_name = serializers.StringRelatedField(source='office_assignment.office.name', read_only=True)
    kpi_title = serializers.StringRelatedField(source='office_assignment.university_assignment.kpi.title', read_only=True)
    academic_year_name = serializers.StringRelatedField(source='office_assignment.university_assignment.academic_year.name', read_only=True)
    measurement_unit_symbol = serializers.StringRelatedField(source='office_assignment.university_assignment.kpi.measurement_unit.symbol', read_only=True)

    # Office assignment values
    office_target = serializers.DecimalField(source='office_assignment.target', max_digits=15, decimal_places=2, read_only=True)
    office_min_value = serializers.DecimalField(source='office_assignment.min_value', max_digits=15, decimal_places=2, read_only=True)
    office_max_value = serializers.DecimalField(source='office_assignment.max_value', max_digits=15, decimal_places=2, read_only=True)
    office_baseline_value = serializers.DecimalField(source='office_assignment.baseline_value', max_digits=15, decimal_places=2, read_only=True)
    office_baseline_year = serializers.CharField(source='office_assignment.baseline_year', read_only=True)
    office_start_date = serializers.DateField(source='office_assignment.start_date', read_only=True)
    office_target_date = serializers.DateField(source='office_assignment.target_date', read_only=True)

    # University assignment values
    university_target = serializers.DecimalField(source='office_assignment.university_assignment.target', max_digits=15, decimal_places=2, read_only=True)
    university_min_value = serializers.DecimalField(source='office_assignment.university_assignment.min_value', max_digits=15, decimal_places=2, read_only=True)
    university_max_value = serializers.DecimalField(source='office_assignment.university_assignment.max_value', max_digits=15, decimal_places=2, read_only=True)
    university_baseline_value = serializers.DecimalField(source='office_assignment.university_assignment.baseline_value', max_digits=15, decimal_places=2, read_only=True)
    university_baseline_year = serializers.CharField(source='office_assignment.university_assignment.baseline_year', read_only=True)
    university_start_date = serializers.DateField(source='office_assignment.university_assignment.start_date', read_only=True)
    university_target_date = serializers.DateField(source='office_assignment.university_assignment.target_date', read_only=True)

    current_value = serializers.DecimalField(max_digits=15, decimal_places=2, read_only=True)
    progress = serializers.SerializerMethodField(read_only=True)
    status_color = serializers.CharField(read_only=True)
    on_track = serializers.BooleanField(read_only=True)
    kpi_direction = serializers.CharField(source='office_assignment.university_assignment.kpi.direction', read_only=True)

    # Add theme and subtheme names
    theme_name = serializers.StringRelatedField(source='office_assignment.university_assignment.kpi.theme.name', read_only=True)
    sub_theme_name = serializers.StringRelatedField(source='office_assignment.university_assignment.kpi.sub_theme.name', read_only=True)
    kpi_theme_name = serializers.StringRelatedField(source='office_assignment.university_assignment.kpi.theme.name', read_only=True)
    kpi_sub_theme_name = serializers.StringRelatedField(source='office_assignment.university_assignment.kpi.sub_theme.name', read_only=True)

    class Meta:
        model = UserKPIAssignment
        fields = [
            'id', 'office_assignment', 'office_assignment_id', 'user', 'user_username', 'user_email', 'user_full_name',
            'office_name', 'kpi_title', 'academic_year_name', 'weight',
            'target', 'min_value', 'max_value', 'baseline_value', 'baseline_year', 'start_date', 'target_date',
            'office_target', 'office_min_value', 'office_max_value', 'office_baseline_value',
            'office_baseline_year', 'office_start_date', 'office_target_date',
            'university_target', 'university_min_value', 'university_max_value', 'university_baseline_value',
            'university_baseline_year', 'university_start_date', 'university_target_date',
            'notes', 'is_active', 'created_at', 'updated_at', 'measurement_unit_symbol',
            'current_value', 'progress', 'status_color', 'on_track', 'kpi_direction',
            'theme_name', 'sub_theme_name', 'kpi_theme_name', 'kpi_sub_theme_name'
        ]
        read_only_fields = ['created_at', 'updated_at', 'user_username', 'user_email', 'user_full_name',
                           'current_value', 'progress', 'status_color', 'on_track',
                           'theme_name', 'sub_theme_name', 'kpi_theme_name', 'kpi_sub_theme_name']

    def get_user_full_name(self, obj):
        return f"{obj.user.first_name} {obj.user.last_name}".strip() or obj.user.username

    def get_progress(self, obj):
        return obj.calculate_progress()

    def validate(self, data):
        """
        Validate the assignment data.
        """
        # Print the data for debugging
        import logging
        logger = logging.getLogger(__name__)
        logger.info(f"UserKPIAssignmentSerializer validate data: {data}")

        # Handle empty strings for date fields
        if 'start_date' in data and data['start_date'] == '':
            data['start_date'] = None

        if 'target_date' in data and data['target_date'] == '':
            data['target_date'] = None

        if 'baseline_year' in data and data['baseline_year'] == '':
            data['baseline_year'] = None

        # Ensure required fields are present
        if 'office_assignment' not in data:
            raise serializers.ValidationError({'office_assignment': 'This field is required.'})

        if 'user' not in data:
            raise serializers.ValidationError({'user': 'This field is required.'})

        if 'weight' not in data:
            raise serializers.ValidationError({'weight': 'This field is required.'})

        # Validate weight
        weight = data.get('weight')
        if weight is not None:
            if weight < 0 or weight > 100:
                raise serializers.ValidationError({'weight': 'Weight must be between 0 and 100'})

            # Check if this is a new assignment or an update
            office_assignment = None
            if self.instance:
                # This is an update
                office_assignment = self.instance.office_assignment
            elif 'office_assignment' in data:
                # This is a new assignment
                office_assignment = data['office_assignment']

            if office_assignment:
                # Get all existing user assignments for this office assignment
                existing_assignments = UserKPIAssignment.objects.filter(
                    office_assignment=office_assignment,
                    is_active=True
                )

                # If this is an update, exclude the current instance
                if self.instance:
                    existing_assignments = existing_assignments.exclude(pk=self.instance.pk)

                # Calculate the total weight of existing assignments
                total_weight = existing_assignments.aggregate(Sum('weight'))['weight__sum'] or 0

                # Add the new weight
                new_total_weight = total_weight + weight

                if new_total_weight > 100:
                    raise serializers.ValidationError({
                        'weight': f'Total weight of all user assignments for this KPI would exceed 100%. Current total: {total_weight}%, New weight: {weight}%, New total would be: {new_total_weight}%'
                    })

        return data

    def create(self, validated_data):
        """
        Create a new user KPI assignment.
        """
        # Print the validated data for debugging
        import logging
        logger = logging.getLogger(__name__)
        logger.info(f"UserKPIAssignmentSerializer create validated_data: {validated_data}")

        try:
            return super().create(validated_data)
        except Exception as e:
            logger.error(f"Error creating user KPI assignment: {str(e)}")
            raise serializers.ValidationError(f"Error creating user KPI assignment: {str(e)}")

    def update(self, instance, validated_data):
        """
        Update an existing user KPI assignment.
        """
        # Print the validated data for debugging
        import logging
        logger = logging.getLogger(__name__)
        logger.info(f"UserKPIAssignmentSerializer update validated_data: {validated_data}")

        try:
            return super().update(instance, validated_data)
        except Exception as e:
            logger.error(f"Error updating user KPI assignment: {str(e)}")
            raise serializers.ValidationError(f"Error updating user KPI assignment: {str(e)}")


class KPIReportSerializer(serializers.ModelSerializer):
    university_name = serializers.StringRelatedField(source='university_assignment.university.name', read_only=True)
    kpi_title = serializers.StringRelatedField(source='university_assignment.kpi.title', read_only=True)
    academic_year_name = serializers.StringRelatedField(source='university_assignment.academic_year.name', read_only=True)
    measurement_unit_symbol = serializers.StringRelatedField(source='university_assignment.kpi.measurement_unit.symbol', read_only=True)
    target = serializers.DecimalField(source='university_assignment.target', max_digits=15, decimal_places=2, read_only=True)
    reported_by_username = serializers.StringRelatedField(source='reported_by.username', read_only=True)
    reported_by_name = serializers.SerializerMethodField(read_only=True)
    reviewed_by_username = serializers.StringRelatedField(source='reviewed_by.username', read_only=True)
    reviewed_by_name = serializers.SerializerMethodField(read_only=True)
    progress = serializers.DecimalField(max_digits=5, decimal_places=2, read_only=True)

    class Meta:
        model = KPIReport
        fields = [
            'id', 'university_assignment', 'university_name', 'kpi_title',
            'academic_year_name', 'reporting_period', 'value', 'target', 'progress',
            'status', 'notes', 'supporting_document', 'reported_by', 'reported_by_username',
            'reported_by_name', 'reported_at', 'reviewed_by', 'reviewed_by_username',
            'reviewed_by_name', 'reviewed_at', 'review_notes', 'created_at', 'updated_at',
            'measurement_unit_symbol'
        ]
        read_only_fields = [
            'created_at', 'updated_at', 'reported_by', 'reported_at',
            'reviewed_by', 'reviewed_at', 'progress'
        ]

    def get_reported_by_name(self, obj):
        if not obj.reported_by:
            return None
        return f"{obj.reported_by.first_name} {obj.reported_by.last_name}".strip() or obj.reported_by.username

    def get_reviewed_by_name(self, obj):
        if not obj.reviewed_by:
            return None
        return f"{obj.reviewed_by.first_name} {obj.reviewed_by.last_name}".strip() or obj.reviewed_by.username

    def validate(self, data):
        """
        Validate the report data.
        """
        # Handle empty strings for notes fields
        if 'notes' in data and data['notes'] == '':
            data['notes'] = None

        if 'review_notes' in data and data['review_notes'] == '':
            data['review_notes'] = None

        return data

    def create(self, validated_data):
        # Set the reported_by and reported_at fields
        validated_data['reported_by'] = self.context['request'].user
        validated_data['reported_at'] = timezone.now()
        return super().create(validated_data)

    def update(self, instance, validated_data):
        # If this is a review update, set the reviewer and review time
        if 'status' in validated_data and validated_data['status'] in ['under_review', 'approved', 'rejected']:
            if self.context.get('request') and hasattr(self.context['request'], 'user'):
                validated_data['reviewed_by'] = self.context['request'].user
                validated_data['reviewed_at'] = timezone.now()

        return super().update(instance, validated_data)


class OfficeKPIReportSerializer(serializers.ModelSerializer):
    office_name = serializers.StringRelatedField(source='office_assignment.office.name', read_only=True)
    kpi_title = serializers.StringRelatedField(source='office_assignment.university_assignment.kpi.title', read_only=True)
    academic_year_name = serializers.StringRelatedField(source='office_assignment.university_assignment.academic_year.name', read_only=True)
    measurement_unit_symbol = serializers.StringRelatedField(source='office_assignment.university_assignment.kpi.measurement_unit.symbol', read_only=True)
    target = serializers.DecimalField(source='office_assignment.target', max_digits=15, decimal_places=2, read_only=True)
    reported_by_username = serializers.StringRelatedField(source='reported_by.username', read_only=True)
    reported_by_name = serializers.SerializerMethodField(read_only=True)
    reviewed_by_username = serializers.StringRelatedField(source='reviewed_by.username', read_only=True)
    reviewed_by_name = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = OfficeKPIReport
        fields = [
            'id', 'office_assignment', 'office_name', 'kpi_title',
            'academic_year_name', 'reporting_period', 'value', 'target',
            'status', 'notes', 'supporting_document', 'reported_by', 'reported_by_username',
            'reported_by_name', 'reported_at', 'reviewed_by', 'reviewed_by_username',
            'reviewed_by_name', 'reviewed_at', 'review_notes', 'created_at', 'updated_at',
            'measurement_unit_symbol'
        ]
        read_only_fields = [
            'created_at', 'updated_at', 'reported_by', 'reported_at',
            'reviewed_by', 'reviewed_at'
        ]

    def get_reported_by_name(self, obj):
        if not obj.reported_by:
            return None
        return f"{obj.reported_by.first_name} {obj.reported_by.last_name}".strip() or obj.reported_by.username

    def get_reviewed_by_name(self, obj):
        if not obj.reviewed_by:
            return None
        return f"{obj.reviewed_by.first_name} {obj.reviewed_by.last_name}".strip() or obj.reviewed_by.username

    def create(self, validated_data):
        # Set the reported_by and reported_at fields
        validated_data['reported_by'] = self.context['request'].user
        validated_data['reported_at'] = timezone.now()
        return super().create(validated_data)


class UserKPIReportSerializer(serializers.ModelSerializer):
    user_username = serializers.StringRelatedField(source='user_assignment.user.username', read_only=True)
    user_name = serializers.SerializerMethodField(read_only=True)
    office_name = serializers.StringRelatedField(source='user_assignment.office_assignment.office.name', read_only=True)
    kpi_title = serializers.StringRelatedField(source='user_assignment.office_assignment.university_assignment.kpi.title', read_only=True)
    academic_year_name = serializers.StringRelatedField(source='user_assignment.office_assignment.university_assignment.academic_year.name', read_only=True)
    measurement_unit_symbol = serializers.StringRelatedField(source='user_assignment.office_assignment.university_assignment.kpi.measurement_unit.symbol', read_only=True)
    target = serializers.DecimalField(source='user_assignment.target', max_digits=15, decimal_places=2, read_only=True)
    reviewed_by_username = serializers.StringRelatedField(source='reviewed_by.username', read_only=True)
    reviewed_by_name = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = UserKPIReport
        fields = [
            'id', 'user_assignment', 'user_username', 'user_name', 'office_name',
            'kpi_title', 'academic_year_name', 'reporting_period', 'value', 'target',
            'status', 'notes', 'supporting_document', 'reported_at',
            'reviewed_by', 'reviewed_by_username', 'reviewed_by_name',
            'reviewed_at', 'review_notes', 'created_at', 'updated_at',
            'measurement_unit_symbol'
        ]
        read_only_fields = [
            'created_at', 'updated_at', 'reported_at',
            'reviewed_by', 'reviewed_at'
        ]

    def get_user_name(self, obj):
        user = obj.user_assignment.user
        return f"{user.first_name} {user.last_name}".strip() or user.username

    def get_reviewed_by_name(self, obj):
        if not obj.reviewed_by:
            return None
        return f"{obj.reviewed_by.first_name} {obj.reviewed_by.last_name}".strip() or obj.reviewed_by.username

    def create(self, validated_data):
        # Set the reported_at field
        validated_data['reported_at'] = timezone.now()
        return super().create(validated_data)
