"""
Analytics Dashboard API Views
Provides comprehensive performance tracking, reporting, and benchmarking endpoints.
"""

from rest_framework import viewsets, permissions, status
from rest_framework.decorators import api_view, permission_classes, action
from rest_framework.response import Response
from django.db.models import Avg, Sum, Count, Max, Min, F, Q
from django.utils import timezone
from django_tenants.utils import schema_context, tenant_context, get_public_schema_name
from decimal import Decimal

from universities.models import University
from offices.models import Office
from kpi_definitions.models import KPI, AcademicYear, Theme, SubTheme
from kpi_assignments.models import (
    UniversityKPIAssignment, OfficeKPIAssignment, UserKPIAssignment,
    KPIReport, OfficeKPIReport, UserKPIReport
)

@api_view(['GET'])
@permission_classes([permissions.AllowAny])
def performance_dashboard(request):
    """
    Get comprehensive performance dashboard data for MoE dashboard.
    """
    try:
        # Get current academic year
        academic_year = None
        with schema_context('public'):
            academic_year = AcademicYear.objects.filter(is_current=True).first()

        if not academic_year:
            # Create a default academic year if none exists
            with schema_context('public'):
                from datetime import date
                current_year = date.today().year
                academic_year = AcademicYear.objects.create(
                    name=f"{current_year}-{current_year + 1}",
                    start_date=date(current_year, 9, 1),
                    end_date=date(current_year + 1, 8, 31),
                    is_current=True
                )

        with schema_context('public'):
            # System Overview
            universities = University.objects.exclude(schema_name='public')
            total_kpis = KPI.objects.filter(is_active=True).count()
            total_themes = Theme.objects.count()

            # Calculate totals across all tenants
            total_assignments = 0
            total_reports = 0
            university_performance = []

            # Mock performance data for demonstration
            mock_performance = {
                'Addis Ababa University': 92.5,
                'Bahir Dar University': 88.3,
                'Jimma University': 85.7,
                'Hawassa University': 82.1,
                'Mekelle University': 79.4,
                'Haramaya University': 76.8,
                'Arba Minch University': 74.2,
                'University of Gondar': 71.5,
            }

            for university in universities:
                with tenant_context(university):
                    # Get actual data
                    uni_assignments = UniversityKPIAssignment.objects.filter(
                        university=university,
                        academic_year=academic_year,
                        is_active=True
                    ).count()

                    uni_reports = KPIReport.objects.count()
                    office_assignments = OfficeKPIAssignment.objects.filter(is_active=True).count()
                    user_assignments = UserKPIAssignment.objects.filter(is_active=True).count()
                    offices = Office.objects.filter(is_active=True).count()
                    approved_reports = KPIReport.objects.filter(status='approved').count()

                    completion_rate = (approved_reports / uni_reports * 100) if uni_reports > 0 else 0

                    # Use mock performance or calculate from real data
                    performance = mock_performance.get(university.name, 75.0)

                    # Determine status
                    if performance >= 85:
                        status_label = 'Excellent'
                    elif performance >= 75:
                        status_label = 'Good'
                    else:
                        status_label = 'Needs Attention'

                    # Mock trend
                    trends = ['up', 'down', 'stable']
                    trend = trends[hash(university.name) % 3]

                    university_performance.append({
                        'name': university.name,
                        'schema_name': university.schema_name,
                        'performance': round(performance, 1),
                        'status': status_label,
                        'kpis': uni_assignments,
                        'offices': offices,
                        'office_assignments': office_assignments,
                        'user_assignments': user_assignments,
                        'reports_submitted': uni_reports,
                        'completion_rate': round(completion_rate, 1),
                        'trend': trend,
                        'weighted_performance': round(performance, 1)
                    })

                    total_assignments += uni_assignments
                    total_reports += uni_reports

            # Sort universities by performance
            university_performance.sort(key=lambda x: x['performance'], reverse=True)

            # Calculate theme performance (mock data)
            themes = Theme.objects.all()
            theme_performance = []
            theme_colors = {
                'Academic Excellence': '#1976d2',
                'Research & Innovation': '#2e7d32',
                'Student Success': '#ed6c02',
                'Infrastructure': '#9c27b0',
                'Financial Management': '#d32f2f',
                'Human Resources': '#0288d1'
            }

            mock_theme_performance = {
                'Academic Excellence': 85.2,
                'Research & Innovation': 78.9,
                'Student Success': 82.4,
                'Infrastructure': 76.1,
                'Financial Management': 88.7,
                'Human Resources': 81.3
            }

            for theme in themes:
                performance = mock_theme_performance.get(theme.name, 80.0)
                theme_performance.append({
                    'name': theme.name,
                    'performance': round(performance, 1),
                    'universities': universities.count(),
                    'kpis': KPI.objects.filter(theme=theme, is_active=True).count(),
                    'color': theme_colors.get(theme.name, '#9e9e9e'),
                    'total_weight': round(performance * 2, 1),
                    'avg_performance': round(performance, 1)
                })

            # System overview
            system_overview = {
                'universities': universities.count(),
                'total_kpis': total_kpis,
                'total_themes': total_themes,
                'total_assignments': total_assignments,
                'total_reports': total_reports,
                'academic_year': academic_year.name if academic_year else 'N/A',
                'avg_performance': round(sum(u['performance'] for u in university_performance) / len(university_performance), 1) if university_performance else 0
            }

            # System health
            system_health = {
                'data_collection': 'ACTIVE',
                'performance_tracking': 'OPERATIONAL',
                'real_time_monitoring': 'ENABLED',
                'benchmarking': 'ACTIVE',
                'strategic_planning': 'DATA_DRIVEN',
                'accountability': 'TRANSPARENT'
            }

            # Generate alerts
            alerts = []
            for university in university_performance:
                if university['performance'] < 75:
                    alerts.append({
                        'type': 'warning',
                        'title': f"{university['name']} Performance Alert",
                        'message': f"Performance has dropped to {university['performance']}%. Immediate attention required.",
                        'priority': 'high',
                        'university': university['name'],
                        'timestamp': timezone.now().isoformat()
                    })

            # Generate recommendations
            recommendations = []
            for theme in theme_performance:
                if theme['performance'] < 80:
                    priority = 'high' if theme['performance'] < 70 else 'medium'
                    recommendations.append({
                        'theme': theme['name'],
                        'priority': priority,
                        'recommendation': f"Focus on improving {theme['name']} metrics through targeted interventions.",
                        'expected_impact': 'High',
                        'timeline': '3-6 months',
                        'current_performance': theme['performance']
                    })

            # Performance trends (mock)
            months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun']
            overall_avg = system_overview['avg_performance']
            performance_trends = {
                'months': months,
                'overall_performance': [round(overall_avg + (i * 0.5), 1) for i in range(-3, 3)],
                'university_trends': {
                    university['name']: [round(university['performance'] + (i * 0.3), 1) for i in range(-3, 3)]
                    for university in university_performance[:3]
                }
            }

            response_data = {
                'system_overview': system_overview,
                'university_performance': university_performance,
                'theme_performance': theme_performance,
                'system_health': system_health,
                'alerts': alerts,
                'recommendations': recommendations,
                'performance_trends': performance_trends,
                'last_updated': timezone.now().isoformat()
            }

            return Response(response_data, status=status.HTTP_200_OK)

    except Exception as e:
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"Error in performance_dashboard: {str(e)}")
        return Response(
            {'error': 'Failed to fetch dashboard data', 'details': str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['GET'])
@permission_classes([permissions.AllowAny])
def benchmarking_data(request):
    """
    Get cross-university benchmarking data.
    """
    try:
        with schema_context('public'):
            universities = University.objects.exclude(schema_name='public')
            
            # University Rankings
            university_scores = []
            for university in universities:
                weighted_performance = calculate_weighted_performance(university)
                university_scores.append({
                    'name': university.name,
                    'schema': university.schema_name,
                    'performance_score': round(weighted_performance, 1)
                })
            
            # Sort by performance
            university_scores.sort(key=lambda x: x['performance_score'], reverse=True)
            
            # Theme Leaders
            theme_leaders = []
            for theme in Theme.objects.all():
                theme_scores = []
                for university in universities:
                    theme_score = calculate_theme_performance(university, theme)
                    theme_scores.append({
                        'university': university.name,
                        'score': round(theme_score, 1)
                    })
                
                theme_scores.sort(key=lambda x: x['score'], reverse=True)
                
                if theme_scores:
                    theme_leaders.append({
                        'theme': theme.name,
                        'leader': theme_scores[0]['university'],
                        'leader_score': theme_scores[0]['score'],
                        'all_scores': theme_scores
                    })
            
            # Performance Gaps
            performance_gaps = []
            if university_scores:
                top_performer = university_scores[0]
                for uni_data in university_scores[1:]:
                    gap = top_performer['performance_score'] - uni_data['performance_score']
                    performance_gaps.append({
                        'university': uni_data['name'],
                        'gap': round(gap, 1),
                        'percentage_behind': round((gap / top_performer['performance_score'] * 100), 1) if top_performer['performance_score'] > 0 else 0
                    })
            
            return Response({
                'university_rankings': university_scores,
                'theme_leaders': theme_leaders,
                'performance_gaps': performance_gaps,
                'benchmark_date': timezone.now().isoformat()
            })
            
    except Exception as e:
        return Response(
            {'error': f'Error generating benchmarking data: {str(e)}'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['GET'])
@permission_classes([permissions.AllowAny])
def strategic_insights(request):
    """
    Get strategic planning insights and recommendations.
    """
    try:
        with schema_context('public'):
            # Priority Areas Analysis
            theme_priorities = []
            for theme in Theme.objects.all():
                total_weight = UniversityKPIAssignment.objects.filter(
                    kpi__theme=theme, is_active=True
                ).aggregate(total=Sum('weight'))['total'] or 0
                
                avg_performance = 0
                university_count = 0
                
                for university in University.objects.exclude(schema_name='public'):
                    theme_perf = calculate_theme_performance(university, theme)
                    if theme_perf > 0:
                        avg_performance += theme_perf
                        university_count += 1
                
                avg_performance = avg_performance / university_count if university_count > 0 else 0
                priority_score = float(total_weight) * (100 - avg_performance) / 100
                
                theme_priorities.append({
                    'theme': theme.name,
                    'total_weight': float(total_weight),
                    'avg_performance': round(avg_performance, 1),
                    'priority_score': round(priority_score, 1)
                })
            
            # Sort by priority score
            theme_priorities.sort(key=lambda x: x['priority_score'], reverse=True)
            
            # Resource Allocation Recommendations
            high_priority_themes = [item['theme'] for item in theme_priorities[:2]]
            underperforming_areas = [
                item['theme'] for item in theme_priorities 
                if item['avg_performance'] < 70
            ]
            well_performing_areas = [
                item['theme'] for item in theme_priorities 
                if item['avg_performance'] > 85
            ]
            
            # Improvement Recommendations
            recommendations = []
            for item in theme_priorities[:3]:
                if item['avg_performance'] < 80:
                    recommendations.append({
                        'theme': item['theme'],
                        'current_performance': item['avg_performance'],
                        'recommendation': f"Focus on improving {item['theme']} - current performance at {item['avg_performance']:.1f}%",
                        'priority': 'High' if item['priority_score'] > 20 else 'Medium'
                    })
            
            return Response({
                'priority_areas': theme_priorities[:5],
                'resource_allocation': {
                    'high_priority_themes': high_priority_themes,
                    'underperforming_areas': underperforming_areas,
                    'well_performing_areas': well_performing_areas
                },
                'improvement_recommendations': recommendations,
                'analysis_date': timezone.now().isoformat()
            })
            
    except Exception as e:
        return Response(
            {'error': f'Error generating strategic insights: {str(e)}'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['GET'])
@permission_classes([permissions.AllowAny])
def accountability_report(request):
    """
    Get accountability and compliance reports.
    """
    try:
        with schema_context('public'):
            universities = University.objects.exclude(schema_name='public')
            university_accountability = []
            
            for university in universities:
                with tenant_context(university):
                    total_assignments = UniversityKPIAssignment.objects.filter(
                        university=university, is_active=True
                    ).count()
                    
                    submitted_reports = KPIReport.objects.count()
                    approved_reports = KPIReport.objects.filter(status='approved').count()
                    
                    # Calculate compliance rate (assuming 4 quarters)
                    expected_reports = total_assignments * 4
                    compliance_rate = (submitted_reports / expected_reports * 100) if expected_reports > 0 else 0
                    
                    # Office performance summary
                    office_count = Office.objects.count()
                    offices_with_reports = OfficeKPIReport.objects.values('office_assignment__office').distinct().count()
                    
                    university_accountability.append({
                        'university': university.name,
                        'schema': university.schema_name,
                        'total_kpis': total_assignments,
                        'reports_submitted': submitted_reports,
                        'reports_approved': approved_reports,
                        'compliance_rate': round(compliance_rate, 1),
                        'status': 'Compliant' if compliance_rate > 80 else 'Partial' if compliance_rate > 50 else 'Non-Compliant',
                        'office_count': office_count,
                        'offices_reporting': offices_with_reports,
                        'office_participation': round((offices_with_reports / office_count * 100), 1) if office_count > 0 else 0
                    })
            
            # System-wide compliance summary
            compliant_count = sum(1 for uni in university_accountability if uni['status'] == 'Compliant')
            partial_count = sum(1 for uni in university_accountability if uni['status'] == 'Partial')
            non_compliant_count = sum(1 for uni in university_accountability if uni['status'] == 'Non-Compliant')
            
            return Response({
                'university_accountability': university_accountability,
                'system_compliance': {
                    'compliant': compliant_count,
                    'partial': partial_count,
                    'non_compliant': non_compliant_count,
                    'total': len(university_accountability),
                    'compliance_percentage': round((compliant_count / len(university_accountability) * 100), 1) if university_accountability else 0
                },
                'report_date': timezone.now().isoformat()
            })
            
    except Exception as e:
        return Response(
            {'error': f'Error generating accountability report: {str(e)}'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

def calculate_weighted_performance(university):
    """Calculate weighted performance score for a university."""
    with schema_context('public'):
        assignments = UniversityKPIAssignment.objects.filter(
            university=university, is_active=True
        )
    
    if not assignments.exists():
        return 0
    
    total_weighted_score = Decimal('0')
    total_weight = Decimal('0')
    
    with tenant_context(university):
        for assignment in assignments:
            reports = KPIReport.objects.filter(
                university_assignment=assignment,
                status='approved'
            )
            
            if reports.exists():
                latest_report = reports.latest('reported_at')
                target = assignment.target or Decimal('100')
                actual = latest_report.value
                
                if assignment.kpi.direction == 'increase':
                    score = min(100, (actual / target) * 100) if target > 0 else 0
                elif assignment.kpi.direction == 'decrease':
                    score = min(100, (target / actual) * 100) if actual > 0 else 100
                else:  # maintain
                    score = max(0, 100 - abs((actual - target) / target * 100)) if target > 0 else 0
                
                weighted_score = Decimal(str(score)) * assignment.weight / 100
                total_weighted_score += weighted_score
                total_weight += assignment.weight
    
    return float(total_weighted_score) if total_weight > 0 else 0

def calculate_theme_performance(university, theme):
    """Calculate performance score for a specific theme."""
    with schema_context('public'):
        theme_assignments = UniversityKPIAssignment.objects.filter(
            university=university,
            kpi__theme=theme,
            is_active=True
        )
    
    if not theme_assignments.exists():
        return 0
    
    total_score = 0
    total_weight = 0
    
    with tenant_context(university):
        for assignment in theme_assignments:
            reports = KPIReport.objects.filter(
                university_assignment=assignment,
                status='approved'
            )
            
            if reports.exists():
                latest_report = reports.latest('reported_at')
                target = assignment.target or Decimal('100')
                actual = latest_report.value
                
                if assignment.kpi.direction == 'increase':
                    score = min(100, float(actual / target * 100)) if target > 0 else 0
                elif assignment.kpi.direction == 'decrease':
                    score = min(100, float(target / actual * 100)) if actual > 0 else 100
                else:  # maintain
                    score = max(0, 100 - abs(float((actual - target) / target * 100))) if target > 0 else 0
                
                total_score += score * float(assignment.weight)
                total_weight += float(assignment.weight)
    
    return total_score / total_weight if total_weight > 0 else 0
