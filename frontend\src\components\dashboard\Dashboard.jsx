import { useState, useEffect } from 'react'
import {
  Container, Box, Paper, Typography, Grid, CircularProgress,
  Card, CardContent, Button, IconButton, Divider, List, ListItem,
  ListItemText, ListItemIcon, Tooltip, LinearProgress
} from '@mui/material'
import { useTheme, alpha } from '@mui/material/styles'
import RefreshIcon from '@mui/icons-material/Refresh'
import TrendingUpIcon from '@mui/icons-material/TrendingUp'
import SchoolIcon from '@mui/icons-material/School'
import AssignmentIcon from '@mui/icons-material/Assignment'
import TimelineIcon from '@mui/icons-material/Timeline'
import CheckCircleIcon from '@mui/icons-material/CheckCircle'
import WarningIcon from '@mui/icons-material/Warning'
import ErrorIcon from '@mui/icons-material/Error'
import CategoryIcon from '@mui/icons-material/Category'
import CalendarMonthIcon from '@mui/icons-material/CalendarMonth'
import TenantBanner from './TenantBanner'
import PublicTenantDashboard from './PublicTenantDashboard'
import { getMoEDashboard } from '../../services/dashboardService'
import { aggregateUniversityReports, formatAggregatedData } from '../../services/aggregationService'
import { useTenant } from '../../contexts/TenantContext'

const Dashboard = () => {
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const [dashboardData, setDashboardData] = useState(null)
  const theme = useTheme()
  const { tenant } = useTenant()

  const [performanceData, setPerformanceData] = useState(null)
  const [loadingPerformance, setLoadingPerformance] = useState(true)

  // Check if we're on the public tenant (Ministry of Education)
  const isPublicTenant = tenant?.schema_name === 'public' || tenant?.name === 'Ministry of Education'

  const fetchDashboardData = async () => {
    try {
      setLoading(true)

      // With cookie-based auth, we don't need to check for tokens
      // The API calls will include cookies automatically

      // Fetch dashboard data from the API
      try {
        const data = await getMoEDashboard()
        setDashboardData(data)
      } catch (dashboardErr) {
        console.error('Error fetching dashboard data:', dashboardErr)

        // Check if it's an authentication error
        if (dashboardErr.response?.status === 401) {
          setError('You need to be logged in to view this dashboard.')
          setLoading(false)
          return
        }

        // Create a fallback dashboard data object
        setDashboardData({
          counts: {
            universities: 0,
            kpis: 0,
            assignments: 0,
            themes: 0,
          },
          current_academic_year: null,
          recent_kpis: [],
          recent_assignments: [],
          kpis_by_theme: [],
          performance: null,
          performance_data: null,
        })
      }

      // Fetch aggregated performance data
      setLoadingPerformance(true)
      // The aggregateUniversityReports function now handles all errors internally
      // and always returns a valid object, never throws
      const performanceResult = await aggregateUniversityReports()
      setPerformanceData(formatAggregatedData(performanceResult))

      setError(null)
    } catch (err) {
      console.error('Error in dashboard initialization:', err)
      if (err.response?.status === 401) {
        setError('Your session has expired. Please log in again.')
      } else {
        setError('Failed to load dashboard data. Please try again later.')
      }
    } finally {
      setLoading(false)
      setLoadingPerformance(false)
    }
  }

  useEffect(() => {
    fetchDashboardData()
  }, [])

  // Show PublicTenantDashboard for Ministry of Education (public tenant)
  if (isPublicTenant) {
    return <PublicTenantDashboard />
  }

  if (loading) {
    return (
      <Box sx={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        minHeight: '80vh'
      }}>
        <CircularProgress />
      </Box>
    )
  }

  if (error) {
    return (
      <Container maxWidth="lg" sx={{ mt: 4 }}>
        <Paper
          elevation={3}
          sx={{
            p: 4,
            textAlign: 'center',
            backgroundColor: theme.palette.error.light,
            color: theme.palette.error.contrastText
          }}
        >
          <Typography variant="h6" gutterBottom>{error}</Typography>
          <Box sx={{ mt: 2, display: 'flex', justifyContent: 'center', gap: 2 }}>
            <Button
              variant="contained"
              onClick={fetchDashboardData}
              startIcon={<RefreshIcon />}
              sx={{
                backgroundColor: theme.palette.primary.main,
                '&:hover': { backgroundColor: theme.palette.primary.dark }
              }}
            >
              Try Again
            </Button>
            {error.includes('session has expired') && (
              <Button
                variant="outlined"
                color="inherit"
                href="/login"
                sx={{ borderColor: 'currentColor' }}
              >
                Go to Login
              </Button>
            )}
          </Box>
        </Paper>
      </Container>
    )
  }

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      {/* Enhanced Tenant Banner */}
      <TenantBanner />

      {/* Header */}
      <Box sx={{ mb: 4, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Typography variant="h4" component="h1" sx={{ fontWeight: 'bold', color: theme.palette.primary.main }}>
          Dashboard Overview
        </Typography>
        <IconButton onClick={fetchDashboardData} title="Refresh data">
          <RefreshIcon />
        </IconButton>
      </Box>

      {/* Stats Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <StatsCard
            title="Universities"
            value={dashboardData?.counts?.universities || 0}
            icon={<SchoolIcon />}
            color="#2196f3"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatsCard
            title="KPIs"
            value={dashboardData?.counts?.kpis || 0}
            icon={<TimelineIcon />}
            color="#4caf50"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatsCard
            title="Assignments"
            value={dashboardData?.counts?.assignments || 0}
            icon={<AssignmentIcon />}
            color="#ff9800"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatsCard
            title="Performance"
            value={performanceData?.performance || `${dashboardData?.performance || 0}%`}
            icon={<TrendingUpIcon />}
            color="#e91e63"
            loading={loadingPerformance}
          />
        </Grid>
      </Grid>

      {/* Performance Data and Theme Breakdown */}
      <Grid container spacing={3}>
        <Grid item xs={12} md={8}>
          <Paper
            sx={{
              p: 3,
              display: 'flex',
              flexDirection: 'column',
              height: 'auto',
              minHeight: 400,
              backgroundColor: theme.palette.background.paper,
              boxShadow: theme.shadows[3]
            }}
          >
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Typography variant="h6">Performance by Theme</Typography>
              {loadingPerformance && <CircularProgress size={24} />}
            </Box>

            {performanceData?.themeBreakdown?.length > 0 ? (
              <>
                <Box sx={{ mb: 3 }}>
                  <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                    Academic Year: {performanceData.academicYear}
                  </Typography>
                  <Typography variant="subtitle2" color="text.secondary">
                    Reporting Period: {performanceData.reportingPeriod}
                  </Typography>
                </Box>

                <List>
                  {performanceData.themeBreakdown.map((themeItem, index) => (
                    <ListItem key={index} sx={{
                      mb: 2,
                      p: 2,
                      borderRadius: 1,
                      bgcolor: alpha(themeItem.performance === 'N/A'
                        ? theme.palette.grey[500]
                        : parseInt(themeItem.performance) >= 80
                          ? theme.palette.success.main
                          : parseInt(themeItem.performance) >= 60
                            ? theme.palette.warning.main
                            : theme.palette.error.main, 0.1)
                    }}>
                      <ListItemIcon>
                        <CategoryIcon color={themeItem.performance === 'N/A'
                          ? 'disabled'
                          : parseInt(themeItem.performance) >= 80
                            ? 'success'
                            : parseInt(themeItem.performance) >= 60
                              ? 'warning'
                              : 'error'}
                        />
                      </ListItemIcon>
                      <ListItemText
                        primary={
                          <Typography variant="subtitle1" fontWeight="medium">
                            {themeItem.name}
                          </Typography>
                        }
                        secondary={
                          <Box sx={{ mt: 1 }}>
                            <Typography variant="body2" color="text.secondary">
                              Weight: {themeItem.weight} • KPIs: {themeItem.kpiCount}
                            </Typography>
                            <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                              <Box sx={{ flexGrow: 1, mr: 2 }}>
                                <LinearProgress
                                  variant="determinate"
                                  value={themeItem.performance === 'N/A' ? 0 : parseInt(themeItem.performance)}
                                  sx={{
                                    height: 8,
                                    borderRadius: 4,
                                    bgcolor: alpha(theme.palette.grey[500], 0.2),
                                    '& .MuiLinearProgress-bar': {
                                      bgcolor: themeItem.performance === 'N/A'
                                        ? theme.palette.grey[500]
                                        : parseInt(themeItem.performance) >= 80
                                          ? theme.palette.success.main
                                          : parseInt(themeItem.performance) >= 60
                                            ? theme.palette.warning.main
                                            : theme.palette.error.main
                                    }
                                  }}
                                />
                              </Box>
                              <Typography variant="body2" fontWeight="bold">
                                {themeItem.performance}
                              </Typography>
                            </Box>
                          </Box>
                        }
                      />
                    </ListItem>
                  ))}
                </List>
              </>
            ) : loadingPerformance ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 300 }}>
                <CircularProgress />
              </Box>
            ) : (
              <Box sx={{ display: 'flex', flexDirection: 'column', justifyContent: 'center', alignItems: 'center', height: 300 }}>
                <Typography variant="body1" color="text.secondary" gutterBottom>
                  {performanceData?.message || "No performance data available"}
                </Typography>
                {performanceData?.error && (
                  <Typography variant="caption" color="error.main" sx={{ mt: 1, textAlign: 'center' }}>
                    {performanceData.error}
                  </Typography>
                )}
                {performanceData?.error?.includes('No current academic year found') && (
                  <Button
                    variant="outlined"
                    color="primary"
                    size="small"
                    sx={{ mt: 2 }}
                    href="/settings/academic-years"
                  >
                    Set Up Academic Year
                  </Button>
                )}
              </Box>
            )}
          </Paper>
        </Grid>

        <Grid item xs={12} md={4}>
          <Paper
            sx={{
              p: 3,
              display: 'flex',
              flexDirection: 'column',
              height: 'auto',
              minHeight: 400,
              backgroundColor: theme.palette.background.paper,
              boxShadow: theme.shadows[3]
            }}
          >
            <Typography variant="h6" sx={{ mb: 2 }}>University Performance</Typography>

            {performanceData?.universityBreakdown?.length > 0 ? (
              <List>
                {performanceData.universityBreakdown
                  .sort((a, b) => {
                    // Sort by performance (highest first)
                    const aVal = a.performance === 'N/A' ? -1 : parseInt(a.performance);
                    const bVal = b.performance === 'N/A' ? -1 : parseInt(b.performance);
                    return bVal - aVal;
                  })
                  .slice(0, 10) // Show top 10
                  .map((univ, index) => (
                    <ListItem key={index} sx={{ mb: 1 }}>
                      <ListItemIcon>
                        {univ.performance === 'N/A' ? (
                          <Tooltip title="No data">
                            <WarningIcon color="disabled" />
                          </Tooltip>
                        ) : parseInt(univ.performance) >= 80 ? (
                          <Tooltip title="On target">
                            <CheckCircleIcon color="success" />
                          </Tooltip>
                        ) : parseInt(univ.performance) >= 60 ? (
                          <Tooltip title="Needs improvement">
                            <WarningIcon color="warning" />
                          </Tooltip>
                        ) : (
                          <Tooltip title="Below target">
                            <ErrorIcon color="error" />
                          </Tooltip>
                        )}
                      </ListItemIcon>
                      <ListItemText
                        primary={univ.name}
                        secondary={`Performance: ${univ.performance}`}
                      />
                    </ListItem>
                  ))}
              </List>
            ) : loadingPerformance ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 300 }}>
                <CircularProgress />
              </Box>
            ) : (
              <Box sx={{ display: 'flex', flexDirection: 'column', justifyContent: 'center', alignItems: 'center', height: 300 }}>
                <Typography variant="body1" color="text.secondary" gutterBottom>
                  {performanceData?.message || "No university data available"}
                </Typography>
                {performanceData?.error && (
                  <Typography variant="caption" color="error.main" sx={{ mt: 1, textAlign: 'center' }}>
                    {performanceData.error}
                  </Typography>
                )}
              </Box>
            )}

            <Divider sx={{ my: 2 }} />

            <Typography variant="h6" sx={{ mb: 2 }}>KPI Summary</Typography>
            <Box sx={{ mb: 2 }}>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                Total KPIs: {performanceData?.kpiCount || 0}
              </Typography>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                Universities: {performanceData?.universityCount || 0}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Reports: {performanceData?.reportsCount || 0}
              </Typography>
            </Box>
          </Paper>
        </Grid>
      </Grid>
    </Container>
  )
}

const StatsCard = ({ title, value, icon, color, loading = false }) => (
  <Card sx={{
    height: '100%',
    backgroundColor: 'white',
    boxShadow: 3,
    transition: 'transform 0.2s',
    '&:hover': { transform: 'translateY(-4px)' }
  }}>
    <CardContent>
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
        <Box sx={{
          p: 1,
          borderRadius: 1,
          backgroundColor: `${color}15`,
          color: color
        }}>
          {icon}
        </Box>
      </Box>
      <Typography variant="h4" component="div" sx={{ fontWeight: 'bold', minHeight: '48px', display: 'flex', alignItems: 'center' }}>
        {loading ? <CircularProgress size={32} /> : value}
      </Typography>
      <Typography variant="body2" color="text.secondary">
        {title}
      </Typography>
    </CardContent>
  </Card>
)

export default Dashboard

