{% extends 'base.html' %}

{% block title %}API Documentation - MoE KPI Tracker{% endblock %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col">
            <h1>MoE KPI Tracker API Documentation</h1>
            <p class="lead">
                This documentation provides information about the MoE KPI Tracker API, which allows universities
                to access their assigned KPIs and report on their progress.
            </p>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h2 class="h5 mb-0">API Overview</h2>
                </div>
                <div class="card-body">
                    <p>
                        The MoE KPI Tracker API is a RESTful API that provides access to the Ministry of Education KPI Tracker system.
                        It allows universities to access their assigned KPIs and report on their progress.
                    </p>
                    <p>
                        <strong>Base URL:</strong> <code>{{ request.scheme }}://{{ request.get_host }}/api/v1/</code>
                    </p>
                    <p>
                        <strong>API Documentation:</strong>
                    </p>
                    <ul>
                        <li>
                            <a href="{% url 'api:schema-swagger-ui' %}" class="btn btn-outline-primary">
                                <i class="fas fa-book"></i> Swagger UI
                            </a>
                            - Interactive API documentation
                        </li>
                        <li class="mt-2">
                            <a href="{% url 'api:schema-redoc' %}" class="btn btn-outline-secondary">
                                <i class="fas fa-file-alt"></i> ReDoc
                            </a>
                            - Alternative API documentation
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h2 class="h5 mb-0">Authentication</h2>
                </div>
                <div class="card-body">
                    <p>
                        All API endpoints require authentication. The API supports the following authentication methods:
                    </p>
                    <h5>Session Authentication</h5>
                    <p>
                        If you're accessing the API from a browser, you can use session authentication.
                        This is the same authentication used for the web interface.
                    </p>
                    <h5>Basic Authentication</h5>
                    <p>
                        For programmatic access, you can use HTTP Basic Authentication.
                        Include an <code>Authorization</code> header with your request:
                    </p>
                    <pre><code>Authorization: Basic &lt;base64-encoded-credentials&gt;</code></pre>
                    <p>
                        Where <code>&lt;base64-encoded-credentials&gt;</code> is the Base64 encoding of <code>username:password</code>.
                    </p>
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        <strong>Warning:</strong> Basic Authentication sends credentials in plain text (Base64 encoded).
                        Always use HTTPS in production.
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h2 class="h5 mb-0">Permissions</h2>
                </div>
                <div class="card-body">
                    <p>
                        The API implements the following permission model:
                    </p>
                    <ul>
                        <li>
                            <strong>MoE Staff (Superusers and Staff Users):</strong>
                            <ul>
                                <li>Full access to all endpoints (read and write)</li>
                                <li>Can manage universities, KPIs, and assignments</li>
                            </ul>
                        </li>
                        <li>
                            <strong>University Users:</strong>
                            <ul>
                                <li>Read-only access to most endpoints</li>
                                <li>Can only access their own university's data</li>
                                <li>Can update their own KPI data (e.g., reporting progress)</li>
                            </ul>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h2 class="h5 mb-0">Available Endpoints</h2>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Endpoint</th>
                                    <th>Description</th>
                                    <th>Methods</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><code>/api/v1/users/</code></td>
                                    <td>List and retrieve users</td>
                                    <td><span class="badge bg-success">GET</span></td>
                                </tr>
                                <tr>
                                    <td><code>/api/v1/universities/</code></td>
                                    <td>List, create, retrieve, update, and delete universities</td>
                                    <td>
                                        <span class="badge bg-success">GET</span>
                                        <span class="badge bg-primary">POST</span>
                                        <span class="badge bg-warning">PUT</span>
                                        <span class="badge bg-danger">DELETE</span>
                                    </td>
                                </tr>
                                <tr>
                                    <td><code>/api/v1/domains/</code></td>
                                    <td>List, create, retrieve, update, and delete domains</td>
                                    <td>
                                        <span class="badge bg-success">GET</span>
                                        <span class="badge bg-primary">POST</span>
                                        <span class="badge bg-warning">PUT</span>
                                        <span class="badge bg-danger">DELETE</span>
                                    </td>
                                </tr>
                                <tr>
                                    <td><code>/api/v1/tags/</code></td>
                                    <td>List, create, retrieve, update, and delete tags</td>
                                    <td>
                                        <span class="badge bg-success">GET</span>
                                        <span class="badge bg-primary">POST</span>
                                        <span class="badge bg-warning">PUT</span>
                                        <span class="badge bg-danger">DELETE</span>
                                    </td>
                                </tr>
                                <tr>
                                    <td><code>/api/v1/academic-years/</code></td>
                                    <td>List, create, retrieve, update, and delete academic years</td>
                                    <td>
                                        <span class="badge bg-success">GET</span>
                                        <span class="badge bg-primary">POST</span>
                                        <span class="badge bg-warning">PUT</span>
                                        <span class="badge bg-danger">DELETE</span>
                                    </td>
                                </tr>
                                <tr>
                                    <td><code>/api/v1/themes/</code></td>
                                    <td>List, create, retrieve, update, and delete themes</td>
                                    <td>
                                        <span class="badge bg-success">GET</span>
                                        <span class="badge bg-primary">POST</span>
                                        <span class="badge bg-warning">PUT</span>
                                        <span class="badge bg-danger">DELETE</span>
                                    </td>
                                </tr>
                                <tr>
                                    <td><code>/api/v1/subthemes/</code></td>
                                    <td>List, create, retrieve, update, and delete subthemes</td>
                                    <td>
                                        <span class="badge bg-success">GET</span>
                                        <span class="badge bg-primary">POST</span>
                                        <span class="badge bg-warning">PUT</span>
                                        <span class="badge bg-danger">DELETE</span>
                                    </td>
                                </tr>
                                <tr>
                                    <td><code>/api/v1/measurement-units/</code></td>
                                    <td>List, create, retrieve, update, and delete measurement units</td>
                                    <td>
                                        <span class="badge bg-success">GET</span>
                                        <span class="badge bg-primary">POST</span>
                                        <span class="badge bg-warning">PUT</span>
                                        <span class="badge bg-danger">DELETE</span>
                                    </td>
                                </tr>
                                <tr>
                                    <td><code>/api/v1/kpis/</code></td>
                                    <td>List, create, retrieve, update, and delete KPIs</td>
                                    <td>
                                        <span class="badge bg-success">GET</span>
                                        <span class="badge bg-primary">POST</span>
                                        <span class="badge bg-warning">PUT</span>
                                        <span class="badge bg-danger">DELETE</span>
                                    </td>
                                </tr>
                                <tr>
                                    <td><code>/api/v1/kpi-assignments/</code></td>
                                    <td>List, create, retrieve, update, and delete KPI assignments</td>
                                    <td>
                                        <span class="badge bg-success">GET</span>
                                        <span class="badge bg-primary">POST</span>
                                        <span class="badge bg-warning">PUT</span>
                                        <span class="badge bg-danger">DELETE</span>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <p class="mt-3">
                        For detailed information about each endpoint, including request and response formats,
                        please refer to the <a href="{% url 'api:schema-swagger-ui' %}">Swagger UI</a> documentation.
                    </p>
                </div>
            </div>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h2 class="h5 mb-0">Example Usage</h2>
                </div>
                <div class="card-body">
                    <h5>Python Example</h5>
                    <pre><code>import requests
from requests.auth import HTTPBasicAuth

# Base URL
base_url = "{{ request.scheme }}://{{ request.get_host }}/api/v1/"

# Authentication
auth = HTTPBasicAuth('username', 'password')

# Get list of KPIs
response = requests.get(f"{base_url}kpis/", auth=auth)
kpis = response.json()

# Print KPI titles
for kpi in kpis['results']:
    print(f"KPI: {kpi['title']}")
    print(f"Description: {kpi['description']}")
    print(f"Theme: {kpi['theme']['name']}")
    print(f"Target: {kpi['target_value']} {kpi['measurement_unit']['symbol']}")
    print("---")</code></pre>

                    <h5 class="mt-4">JavaScript Example</h5>
                    <pre><code>// Base URL
const baseUrl = "{{ request.scheme }}://{{ request.get_host }}/api/v1/";

// Authentication credentials
const username = "username";
const password = "password";

// Encode credentials for Basic Auth
const credentials = btoa(`${username}:${password}`);

// Get list of KPIs
fetch(`${baseUrl}kpis/`, {
    headers: {
        'Authorization': `Basic ${credentials}`
    }
})
.then(response => response.json())
.then(data => {
    // Process KPIs
    data.results.forEach(kpi => {
        console.log(`KPI: ${kpi.title}`);
        console.log(`Description: ${kpi.description}`);
        console.log(`Theme: ${kpi.theme.name}`);
        console.log(`Target: ${kpi.target_value} ${kpi.measurement_unit.symbol}`);
        console.log("---");
    });
})
.catch(error => console.error('Error:', error));</code></pre>
                </div>
            </div>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h2 class="h5 mb-0">Rate Limiting</h2>
                </div>
                <div class="card-body">
                    <p>
                        To ensure fair usage and system stability, the API implements rate limiting.
                        The current rate limits are:
                    </p>
                    <ul>
                        <li><strong>Authenticated users:</strong> 100 requests per minute</li>
                        <li><strong>Anonymous users:</strong> 20 requests per minute</li>
                    </ul>
                    <p>
                        If you exceed these limits, you will receive a <code>429 Too Many Requests</code> response.
                        The response will include a <code>Retry-After</code> header indicating how many seconds
                        to wait before making another request.
                    </p>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h2 class="h5 mb-0">Support</h2>
                </div>
                <div class="card-body">
                    <p>
                        If you have any questions or issues with the API, please contact the Ministry of Education
                        IT support team at <a href="mailto:<EMAIL>"><EMAIL></a>.
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
