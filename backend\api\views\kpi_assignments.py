from rest_framework import viewsets, permissions, status, filters
from rest_framework.response import Response
from rest_framework.decorators import action
from django.db import connection
from django_tenants.utils import get_public_schema_name
from django.db.models import Sum, Avg, Count, F, Q
from django.utils import timezone

from kpi_assignments.models import (
    UniversityKPIAssignment, OfficeKPIAssignment, UserKPIAssignment,
    KPIReport, OfficeKPIReport, UserKPIReport
)
from api.serializers.kpi_assignments import (
    UniversityKPIAssignmentSerializer, OfficeKPIAssignmentSerializer, UserKPIAssignmentSerializer,
    KPIReportSerializer, OfficeKPIReportSerializer, UserKPIReportSerializer
)
from api.permissions import IsMoEStaffOrReadOnly, IsTenantUser
from api.exports import export_university_kpi_assignments_excel, export_university_kpi_assignments_pdf


class UniversityKPIAssignmentViewSet(viewsets.ModelViewSet):
    """
    API endpoint for university KPI assignments.
    Only MoE staff can create, update, or delete assignments.
    """
    queryset = UniversityKPIAssignment.objects.all()
    serializer_class = UniversityKPIAssignmentSerializer
    permission_classes = [permissions.IsAuthenticated, IsMoEStaffOrReadOnly]
    filter_backends = [filters.SearchFilter]
    search_fields = ['university__name', 'kpi__title']

    def get_queryset(self):
        """Filter assignments by university, KPI, or academic year if provided."""
        queryset = UniversityKPIAssignment.objects.all()

        university_id = self.request.query_params.get('university_id')
        if university_id:
            queryset = queryset.filter(university_id=university_id)

        kpi_id = self.request.query_params.get('kpi_id')
        if kpi_id:
            queryset = queryset.filter(kpi_id=kpi_id)

        academic_year_id = self.request.query_params.get('academic_year_id')
        if academic_year_id:
            queryset = queryset.filter(academic_year_id=academic_year_id)

        is_active = self.request.query_params.get('is_active')
        if is_active is not None:
            is_active = is_active.lower() == 'true'
            queryset = queryset.filter(is_active=is_active)

        return queryset

    @action(detail=False, methods=['get'])
    def export_excel(self, request):
        """
        Export KPI assignments for a specific university or all universities to Excel.

        Query parameters:
        - university_id (optional): The ID of the university. If not provided, exports for all universities.
        - academic_year_id (optional): The ID of the academic year to filter by
        """
        university_id = request.query_params.get('university_id')
        academic_year_id = request.query_params.get('academic_year_id')

        # Get the current schema name to restrict university tenants to their own data
        from django.db import connection
        current_schema = connection.schema_name

        try:
            response = export_university_kpi_assignments_excel(
                university_id=university_id,
                academic_year_id=academic_year_id,
                current_schema=current_schema
            )
            return response
        except Exception as e:
            return Response(
                {"detail": f"Error exporting to Excel: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=['get'])
    def export_pdf(self, request):
        """
        Export KPI assignments for a specific university or all universities to PDF.

        Query parameters:
        - university_id (optional): The ID of the university. If not provided, exports for all universities.
        - academic_year_id (optional): The ID of the academic year to filter by
        """
        university_id = request.query_params.get('university_id')
        academic_year_id = request.query_params.get('academic_year_id')

        # Get the current schema name to restrict university tenants to their own data
        from django.db import connection
        current_schema = connection.schema_name

        try:
            response = export_university_kpi_assignments_pdf(
                university_id=university_id,
                academic_year_id=academic_year_id,
                current_schema=current_schema
            )
            return response
        except Exception as e:
            return Response(
                {"detail": f"Error exporting to PDF: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class OfficeKPIAssignmentViewSet(viewsets.ModelViewSet):
    """
    API endpoint for office KPI assignments.
    """
    queryset = OfficeKPIAssignment.objects.all()
    serializer_class = OfficeKPIAssignmentSerializer
    permission_classes = [permissions.IsAuthenticated, IsTenantUser]
    filter_backends = [filters.SearchFilter]
    search_fields = ['office__name', 'university_assignment__kpi__title']

    def get_queryset(self):
        """Filter assignments by office, university assignment, or is_active."""
        queryset = OfficeKPIAssignment.objects.all()

        office_id = self.request.query_params.get('office_id')
        if office_id:
            queryset = queryset.filter(office_id=office_id)

        university_assignment_id = self.request.query_params.get('university_assignment_id')
        if university_assignment_id:
            queryset = queryset.filter(university_assignment_id=university_assignment_id)

        is_active = self.request.query_params.get('is_active')
        if is_active is not None:
            is_active = is_active.lower() == 'true'
            queryset = queryset.filter(is_active=is_active)

        return queryset

    @action(detail=True, methods=['get'])
    def user_assignments(self, request, pk=None):
        """Get all user assignments for this office assignment."""
        office_assignment = self.get_object()
        user_assignments = office_assignment.user_assignments.all()
        serializer = UserKPIAssignmentSerializer(user_assignments, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['get'])
    def reports(self, request, pk=None):
        """Get all reports for this office assignment."""
        office_assignment = self.get_object()
        reports = office_assignment.reports.all()
        serializer = OfficeKPIReportSerializer(reports, many=True)
        return Response(serializer.data)


class UserKPIAssignmentViewSet(viewsets.ModelViewSet):
    """
    API endpoint for user KPI assignments.
    """
    queryset = UserKPIAssignment.objects.all()
    serializer_class = UserKPIAssignmentSerializer
    permission_classes = [permissions.IsAuthenticated, IsTenantUser]
    filter_backends = [filters.SearchFilter]
    search_fields = ['user__username', 'user__email', 'office_assignment__office__name']

    def get_queryset(self):
        """Filter assignments by user, office assignment, or is_active."""
        queryset = UserKPIAssignment.objects.all()

        user_id = self.request.query_params.get('user_id')
        if user_id:
            queryset = queryset.filter(user_id=user_id)

        office_assignment_id = self.request.query_params.get('office_assignment_id')
        if office_assignment_id:
            queryset = queryset.filter(office_assignment_id=office_assignment_id)

        is_active = self.request.query_params.get('is_active')
        if is_active is not None:
            is_active = is_active.lower() == 'true'
            queryset = queryset.filter(is_active=is_active)

        # Filter by current user if requested
        my_assignments = self.request.query_params.get('my_assignments')
        if my_assignments and my_assignments.lower() == 'true':
            queryset = queryset.filter(user=self.request.user)

        return queryset

    @action(detail=True, methods=['get'])
    def reports(self, request, pk=None):
        """Get all reports for this user assignment."""
        user_assignment = self.get_object()
        reports = user_assignment.reports.all()
        serializer = UserKPIReportSerializer(reports, many=True)
        return Response(serializer.data)


class KPIReportViewSet(viewsets.ModelViewSet):
    """
    API endpoint for KPI reports at the university level.
    """
    queryset = KPIReport.objects.all()
    serializer_class = KPIReportSerializer
    permission_classes = [permissions.IsAuthenticated, IsTenantUser]
    filter_backends = [filters.SearchFilter]
    search_fields = ['university_assignment__university__name', 'university_assignment__kpi__title', 'reporting_period']

    def get_queryset(self):
        """Filter reports by university assignment, reporting period, or status."""
        queryset = KPIReport.objects.all()

        university_assignment_id = self.request.query_params.get('university_assignment_id')
        if university_assignment_id:
            queryset = queryset.filter(university_assignment_id=university_assignment_id)

        reporting_period = self.request.query_params.get('reporting_period')
        if reporting_period:
            queryset = queryset.filter(reporting_period=reporting_period)

        status_filter = self.request.query_params.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)

        # Filter by current user if requested
        my_reports = self.request.query_params.get('my_reports')
        if my_reports and my_reports.lower() == 'true':
            queryset = queryset.filter(reported_by=self.request.user)

        return queryset

    @action(detail=True, methods=['post'])
    def review(self, request, pk=None):
        """Review a report and update its status."""
        report = self.get_object()
        status = request.data.get('status')
        review_notes = request.data.get('review_notes')

        if status not in ['submitted', 'under_review', 'approved', 'rejected']:
            return Response(
                {"detail": "Invalid status. Must be 'submitted', 'under_review', 'approved', or 'rejected'."},
                status=status.HTTP_400_BAD_REQUEST
            )

        report.status = status
        report.review_notes = review_notes
        report.reviewed_by = request.user
        report.reviewed_at = timezone.now()
        report.save()

        serializer = self.get_serializer(report)
        return Response(serializer.data)


class OfficeKPIReportViewSet(viewsets.ModelViewSet):
    """
    API endpoint for KPI reports at the office level.
    """
    queryset = OfficeKPIReport.objects.all()
    serializer_class = OfficeKPIReportSerializer
    permission_classes = [permissions.IsAuthenticated, IsTenantUser]
    filter_backends = [filters.SearchFilter]
    search_fields = ['office_assignment__office__name', 'office_assignment__university_assignment__kpi__title', 'reporting_period']

    def get_queryset(self):
        """Filter reports by office assignment, reporting period, or status."""
        queryset = OfficeKPIReport.objects.all()

        office_assignment_id = self.request.query_params.get('office_assignment_id')
        if office_assignment_id:
            queryset = queryset.filter(office_assignment_id=office_assignment_id)

        reporting_period = self.request.query_params.get('reporting_period')
        if reporting_period:
            queryset = queryset.filter(reporting_period=reporting_period)

        status_filter = self.request.query_params.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)

        # Filter by current user if requested
        my_reports = self.request.query_params.get('my_reports')
        if my_reports and my_reports.lower() == 'true':
            queryset = queryset.filter(reported_by=self.request.user)

        return queryset

    @action(detail=True, methods=['post'])
    def review(self, request, pk=None):
        """Review a report and update its status."""
        report = self.get_object()
        status = request.data.get('status')
        review_notes = request.data.get('review_notes')

        if status not in ['submitted', 'under_review', 'approved', 'rejected']:
            return Response(
                {"detail": "Invalid status. Must be 'submitted', 'under_review', 'approved', or 'rejected'."},
                status=status.HTTP_400_BAD_REQUEST
            )

        report.status = status
        report.review_notes = review_notes
        report.reviewed_by = request.user
        report.reviewed_at = timezone.now()
        report.save()

        serializer = self.get_serializer(report)
        return Response(serializer.data)


class UserKPIReportViewSet(viewsets.ModelViewSet):
    """
    API endpoint for KPI reports at the user level.
    """
    queryset = UserKPIReport.objects.all()
    serializer_class = UserKPIReportSerializer
    permission_classes = [permissions.IsAuthenticated, IsTenantUser]
    filter_backends = [filters.SearchFilter]
    search_fields = ['user_assignment__user__username', 'user_assignment__office_assignment__university_assignment__kpi__title', 'reporting_period']

    def get_queryset(self):
        """Filter reports by user assignment, reporting period, or status."""
        queryset = UserKPIReport.objects.all()

        user_assignment_id = self.request.query_params.get('user_assignment_id')
        if user_assignment_id:
            queryset = queryset.filter(user_assignment_id=user_assignment_id)

        reporting_period = self.request.query_params.get('reporting_period')
        if reporting_period:
            queryset = queryset.filter(reporting_period=reporting_period)

        status_filter = self.request.query_params.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)

        # Filter by current user if requested
        my_reports = self.request.query_params.get('my_reports')
        if my_reports and my_reports.lower() == 'true':
            queryset = queryset.filter(user_assignment__user=self.request.user)

        return queryset

    @action(detail=True, methods=['post'])
    def review(self, request, pk=None):
        """Review a report and update its status."""
        report = self.get_object()
        status = request.data.get('status')
        review_notes = request.data.get('review_notes')

        if status not in ['submitted', 'under_review', 'approved', 'rejected']:
            return Response(
                {"detail": "Invalid status. Must be 'submitted', 'under_review', 'approved', or 'rejected'."},
                status=status.HTTP_400_BAD_REQUEST
            )

        report.status = status
        report.review_notes = review_notes
        report.reviewed_by = request.user
        report.reviewed_at = timezone.now()
        report.save()

        serializer = self.get_serializer(report)
        return Response(serializer.data)
