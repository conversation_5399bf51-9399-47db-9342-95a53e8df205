{% extends "drf-yasg/swagger-ui.html" %}
{% load static %}

{% block title %}MoE KPI Tracker API Documentation{% endblock %}

{% block extra_styles %}
<link rel="stylesheet" type="text/css" href="{% static 'drf-yasg/style.css' %}"/>
<style>
    .swagger-ui .topbar {
        background-color: #2c3e50;
    }
    .swagger-ui .info .title {
        color: #2c3e50;
    }
    .swagger-ui .btn.authorize {
        background-color: #27ae60;
        color: #fff;
    }
    .swagger-ui .btn.authorize svg {
        fill: #fff;
    }
    .swagger-ui section.models {
        border: 1px solid #ccc;
    }
    .swagger-ui section.models.is-open h4 {
        border-bottom: 1px solid #ccc;
    }
    .swagger-ui .opblock.opblock-get {
        border-color: #61affe;
        background: rgba(97, 175, 254, 0.1);
    }
    .swagger-ui .opblock.opblock-post {
        border-color: #49cc90;
        background: rgba(73, 204, 144, 0.1);
    }
    .swagger-ui .opblock.opblock-put {
        border-color: #fca130;
        background: rgba(252, 161, 48, 0.1);
    }
    .swagger-ui .opblock.opblock-delete {
        border-color: #f93e3e;
        background: rgba(249, 62, 62, 0.1);
    }
</style>
{% endblock %}

{% block extra_scripts %}
<script type="text/javascript">
    $(document).ready(function() {
        // Add custom JavaScript here if needed
    });
</script>
{% endblock %}

{% block user_context_message %}
<div class="swagger-ui">
    <div class="wrapper">
        <section class="block col-12">
            <div class="info">
                <div class="title">
                    <h2>Ministry of Education KPI Tracker API</h2>
                    <small>Version 1.0</small>
                </div>
                <div class="description">
                    <p>
                        This API provides access to the Ministry of Education KPI Tracker system.
                        It allows universities to access their assigned KPIs and report on their progress.
                    </p>
                    <p>
                        <strong>Authentication:</strong> All API endpoints require authentication.
                        Use the Authorize button above to authenticate.
                    </p>
                    <p>
                        <strong>Permissions:</strong>
                        <ul>
                            <li>MoE staff (superusers and staff users) have full access to all endpoints.</li>
                            <li>University users can only access their own data.</li>
                            <li>Most endpoints are read-only for non-MoE staff users.</li>
                        </ul>
                    </p>
                </div>
            </div>
        </section>
    </div>
</div>
{% endblock %}
