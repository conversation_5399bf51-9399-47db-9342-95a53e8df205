"""
Script to seed user and office KPI assignments for all universities.
This script creates comprehensive KPI assignments for offices and users within each university.
"""

import os
import sys
import django
import logging
import random
from decimal import Decimal
from datetime import date, timedelta

# Add the project directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'moe_kpi_tracker.settings')
django.setup()

# Import after Django setup
from django.contrib.auth.models import User
from django.db import transaction
from django_tenants.utils import schema_context
from universities.models import University
from offices.models import Office, UserPosition, UserProfile
from kpi_definitions.models import KPI, AcademicYear
from kpi_assignments.models import UniversityKPIAssignment, OfficeKPIAssignment, UserKPIAssignment

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def get_current_academic_year():
    """Get the current academic year."""
    try:
        return AcademicYear.objects.filter(is_current=True).first()
    except AcademicYear.DoesNotExist:
        # Create a default academic year if none exists
        current_year = date.today().year
        academic_year = AcademicYear.objects.create(
            name=f"{current_year}-{current_year + 1}",
            start_date=date(current_year, 9, 1),
            end_date=date(current_year + 1, 8, 31),
            is_current=True
        )
        logger.info(f"Created default academic year: {academic_year.name}")
        return academic_year

def create_office_kpi_assignments(university, academic_year):
    """Create KPI assignments for offices within a university."""
    logger.info(f"Creating office KPI assignments for {university.name}")
    
    assignments_created = 0
    
    with schema_context(university.schema_name):
        # Get all active offices
        offices = Office.objects.filter(is_active=True)
        
        # Get university KPI assignments for this academic year
        university_assignments = UniversityKPIAssignment.objects.filter(
            university=university,
            academic_year=academic_year,
            is_active=True
        )
        
        if not university_assignments.exists():
            logger.warning(f"No university KPI assignments found for {university.name}")
            return 0
        
        for university_assignment in university_assignments:
            # Assign KPIs to relevant offices based on KPI theme and office type
            relevant_offices = get_relevant_offices_for_kpi(offices, university_assignment.kpi)
            
            for office in relevant_offices:
                # Check if assignment already exists
                if OfficeKPIAssignment.objects.filter(
                    university_assignment=university_assignment,
                    office=office
                ).exists():
                    continue
                
                # Calculate target and weight based on office level and KPI
                target = calculate_office_target(university_assignment, office)
                weight = calculate_office_weight(university_assignment, office)
                
                # Create office KPI assignment
                office_assignment = OfficeKPIAssignment.objects.create(
                    university_assignment=university_assignment,
                    office=office,
                    weight=weight,
                    target=target,
                    min_value=target * Decimal('0.8'),  # 80% of target as minimum
                    max_value=target * Decimal('1.2'),  # 120% of target as maximum
                    baseline_value=target * Decimal('0.7'),  # 70% of target as baseline
                    baseline_year=str(date.today().year - 1),
                    start_date=academic_year.start_date,
                    target_date=academic_year.end_date,
                    notes=f"Auto-assigned to {office.name}",
                    is_active=True
                )
                
                assignments_created += 1
                logger.info(f"Created office assignment: {office.name} -> {university_assignment.kpi.title}")
    
    return assignments_created

def create_user_kpi_assignments(university, academic_year):
    """Create KPI assignments for users within offices."""
    logger.info(f"Creating user KPI assignments for {university.name}")
    
    assignments_created = 0
    
    with schema_context(university.schema_name):
        # Get all office KPI assignments
        office_assignments = OfficeKPIAssignment.objects.filter(
            university_assignment__academic_year=academic_year,
            is_active=True
        )
        
        for office_assignment in office_assignments:
            # Get users in this office
            office_users = get_office_users(office_assignment.office)
            
            if not office_users:
                continue
            
            # Distribute the office KPI among users
            user_weights = distribute_weights_among_users(office_users, office_assignment)
            
            for user, weight in user_weights.items():
                # Check if assignment already exists
                if UserKPIAssignment.objects.filter(
                    office_assignment=office_assignment,
                    user=user
                ).exists():
                    continue
                
                # Calculate user target based on their weight and role
                user_target = calculate_user_target(office_assignment, user, weight)
                
                # Create user KPI assignment
                user_assignment = UserKPIAssignment.objects.create(
                    office_assignment=office_assignment,
                    user=user,
                    weight=weight,
                    target=user_target,
                    min_value=user_target * Decimal('0.8'),
                    max_value=user_target * Decimal('1.2'),
                    baseline_value=user_target * Decimal('0.7'),
                    baseline_year=str(date.today().year - 1),
                    start_date=academic_year.start_date,
                    target_date=academic_year.end_date,
                    notes=f"Auto-assigned to {user.get_full_name() or user.username}",
                    is_active=True
                )
                
                assignments_created += 1
                logger.info(f"Created user assignment: {user.username} -> {office_assignment.university_assignment.kpi.title}")
    
    return assignments_created

def get_relevant_offices_for_kpi(offices, kpi):
    """Determine which offices are relevant for a specific KPI."""
    # This is a simplified logic - in practice, you might want more sophisticated mapping
    relevant_offices = []
    
    kpi_title_lower = kpi.title.lower()
    kpi_theme_lower = kpi.theme.name.lower() if kpi.theme else ""
    
    for office in offices:
        office_name_lower = office.name.lower()
        
        # Academic KPIs go to academic offices
        if any(keyword in kpi_title_lower or keyword in kpi_theme_lower 
               for keyword in ['academic', 'student', 'research', 'teaching', 'curriculum']):
            if any(keyword in office_name_lower 
                   for keyword in ['academic', 'college', 'department', 'research', 'student']):
                relevant_offices.append(office)
        
        # Administrative KPIs go to administrative offices
        elif any(keyword in kpi_title_lower or keyword in kpi_theme_lower 
                 for keyword in ['admin', 'finance', 'hr', 'human resource', 'budget']):
            if any(keyword in office_name_lower 
                   for keyword in ['admin', 'finance', 'hr', 'human', 'budget', 'president']):
                relevant_offices.append(office)
        
        # General KPIs go to all offices
        else:
            relevant_offices.append(office)
    
    return relevant_offices[:5]  # Limit to top 5 relevant offices

def calculate_office_target(university_assignment, office):
    """Calculate target value for an office based on university target."""
    base_target = university_assignment.target or Decimal('100.0')
    
    # Adjust based on office level (simplified logic)
    office_name_lower = office.name.lower()
    
    if 'president' in office_name_lower:
        return base_target * Decimal('1.1')  # 10% higher for president's office
    elif 'vice' in office_name_lower or 'dean' in office_name_lower:
        return base_target * Decimal('1.05')  # 5% higher for senior offices
    elif 'department' in office_name_lower:
        return base_target * Decimal('0.95')  # 5% lower for departments
    else:
        return base_target

def calculate_office_weight(university_assignment, office):
    """Calculate weight for an office assignment."""
    # Simplified weight calculation - distribute evenly with some variation
    base_weight = Decimal('20.0')  # Base weight of 20%
    
    # Add some randomization
    variation = Decimal(str(random.uniform(-5.0, 5.0)))
    return max(Decimal('5.0'), base_weight + variation)

def get_office_users(office):
    """Get all users working in an office."""
    users = []
    
    # Get users from UserPosition model
    positions = UserPosition.objects.filter(office=office, is_active=True)
    for position in positions:
        users.append(position.user)
    
    # Get users from UserProfile model if no positions found
    if not users:
        profiles = UserProfile.objects.filter(office=office, is_active=True)
        for profile in profiles:
            users.append(profile.user)
    
    # If still no users, get the office manager
    if not users and office.manager:
        users.append(office.manager)
    
    return users

def distribute_weights_among_users(users, office_assignment):
    """Distribute KPI weights among users in an office."""
    if not users:
        return {}
    
    user_weights = {}
    total_weight = Decimal('100.0')
    
    # Identify managers and regular staff
    managers = []
    staff = []
    
    for user in users:
        try:
            position = UserPosition.objects.get(user=user, office=office_assignment.office)
            if position.is_manager:
                managers.append(user)
            else:
                staff.append(user)
        except UserPosition.DoesNotExist:
            try:
                profile = UserProfile.objects.get(user=user)
                if profile.is_manager:
                    managers.append(user)
                else:
                    staff.append(user)
            except UserProfile.DoesNotExist:
                staff.append(user)
    
    # Distribute weights: managers get more weight
    if managers:
        manager_weight = total_weight * Decimal('0.6') / len(managers)  # 60% for managers
        for manager in managers:
            user_weights[manager] = manager_weight
    
    if staff:
        remaining_weight = total_weight - sum(user_weights.values())
        staff_weight = remaining_weight / len(staff)
        for staff_member in staff:
            user_weights[staff_member] = staff_weight
    
    return user_weights

def calculate_user_target(office_assignment, user, weight):
    """Calculate target value for a user based on office target and their weight."""
    office_target = office_assignment.target or Decimal('100.0')
    weight_factor = weight / Decimal('100.0')
    
    # Base user target is proportional to their weight
    user_target = office_target * weight_factor
    
    # Adjust based on user role
    try:
        position = UserPosition.objects.get(user=user, office=office_assignment.office)
        if position.is_manager:
            user_target *= Decimal('1.1')  # 10% higher for managers
    except UserPosition.DoesNotExist:
        pass
    
    return user_target

def seed_university_kpi_assignments(university, academic_year):
    """Seed KPI assignments for a university."""
    logger.info(f"Seeding KPI assignments for {university.name}")
    
    office_assignments = create_office_kpi_assignments(university, academic_year)
    user_assignments = create_user_kpi_assignments(university, academic_year)
    
    logger.info(f"Created {office_assignments} office assignments and {user_assignments} user assignments for {university.name}")
    return office_assignments, user_assignments

def main():
    """Main function to seed user and office KPI assignments."""
    logger.info("Starting user and office KPI assignment seeding process...")
    
    # Get current academic year
    academic_year = get_current_academic_year()
    if not academic_year:
        logger.error("No academic year found. Please create an academic year first.")
        return
    
    # Get all universities except the public tenant
    universities = University.objects.exclude(schema_name='public')
    
    total_office_assignments = 0
    total_user_assignments = 0
    
    for university in universities:
        try:
            with transaction.atomic():
                office_count, user_count = seed_university_kpi_assignments(university, academic_year)
                total_office_assignments += office_count
                total_user_assignments += user_count
        except Exception as e:
            logger.error(f"Error seeding assignments for {university.name}: {str(e)}")
            continue
    
    logger.info("KPI assignment seeding completed.")
    logger.info(f"Total: {total_office_assignments} office assignments and {total_user_assignments} user assignments created")

if __name__ == "__main__":
    main()
