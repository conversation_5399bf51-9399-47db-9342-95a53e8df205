# Seed User and Office KPI Assignments

This directory contains scripts to seed user and office KPI assignments for the MoE KPI Tracker system.

## Overview

The seeding scripts create:
1. **Sample Offices**: Basic organizational structure for each university
2. **Sample Users**: Users with different roles and positions
3. **Office KPI Assignments**: KPI assignments to offices
4. **User KPI Assignments**: KPI assignments to individual users

## Scripts

### 1. `create_seed_users_and_assignments.py`
Main seeding script that creates:
- 5 sample offices per university (Academic Affairs, Student Affairs, Research, Finance, HR)
- 2 users per office (Director and staff member)
- KPI assignments for offices and users

### 2. `seed_user_office_kpi_assignments.py`
Advanced seeding script with more sophisticated logic for:
- Intelligent KPI-to-office mapping based on themes
- Weight distribution algorithms
- Target calculation based on organizational hierarchy

### 3. `run_seed_assignments.py`
Simple runner script that:
- Checks existing data
- Runs the seeding process
- Displays results

## Sample Data Created

### Offices
- **Academic Affairs Office**: Manages academic programs
- **Student Affairs Office**: Handles student services
- **Research Office**: Coordinates research activities
- **Finance Office**: Manages financial resources
- **Human Resources Office**: Manages personnel

### Users per Office
- **Director**: Manager role with higher KPI weights
- **Staff Member**: Regular staff with standard KPI weights

### KPI Assignments
- **Office Assignments**: 2-3 offices per KPI with 15-35% weight distribution
- **User Assignments**: All users in assigned offices with proportional targets

## Usage

### Prerequisites
1. Ensure universities are created and have KPIs assigned
2. Make sure academic year exists
3. Run from the backend directory

### Basic Seeding
```bash
cd backend
python create_seed_users_and_assignments.py
```

### Advanced Seeding
```bash
cd backend
python seed_user_office_kpi_assignments.py
```

### Run with Results Display
```bash
cd backend
python run_seed_assignments.py
```

## Data Structure

### Office Structure
```
University
├── Academic Affairs Office
│   ├── Academic Director (Manager)
│   └── Curriculum Coordinator (Staff)
├── Student Affairs Office
│   ├── Student Director (Manager)
│   └── Student Counselor (Staff)
└── ... (other offices)
```

### Assignment Hierarchy
```
University KPI Assignment
├── Office KPI Assignment (Office A) - 25% weight
│   ├── User Assignment (Director) - 60% of office weight
│   └── User Assignment (Staff) - 40% of office weight
├── Office KPI Assignment (Office B) - 30% weight
│   └── ... (user assignments)
└── ... (other office assignments)
```

## Configuration

### Customizing Offices
Edit the `SAMPLE_OFFICES` list in `create_seed_users_and_assignments.py`:

```python
SAMPLE_OFFICES = [
    {
        "name": "Your Office Name",
        "description": "Office description",
        "users": [
            {
                "username": "user.name",
                "first_name": "First",
                "last_name": "Last",
                "position": "Position Title",
                "is_manager": True/False
            }
        ]
    }
]
```

### Customizing Weights
Modify weight calculation functions:
- `calculate_office_weight()`: Office weight distribution
- `distribute_weights_among_users()`: User weight distribution

### Customizing Targets
Modify target calculation functions:
- `calculate_office_target()`: Office target values
- `calculate_user_target()`: User target values

## Verification

After running the scripts, verify the data:

1. **Check Office Creation**:
   ```python
   from offices.models import Office
   offices = Office.objects.all()
   print(f"Created {offices.count()} offices")
   ```

2. **Check User Creation**:
   ```python
   from django.contrib.auth.models import User
   users = User.objects.all()
   print(f"Created {users.count()} users")
   ```

3. **Check KPI Assignments**:
   ```python
   from kpi_assignments.models import OfficeKPIAssignment, UserKPIAssignment
   office_assignments = OfficeKPIAssignment.objects.all()
   user_assignments = UserKPIAssignment.objects.all()
   print(f"Office assignments: {office_assignments.count()}")
   print(f"User assignments: {user_assignments.count()}")
   ```

## Troubleshooting

### Common Issues

1. **No Academic Year**: Create an academic year first
2. **No University KPIs**: Ensure universities have KPI assignments
3. **Permission Errors**: Run with appropriate database permissions
4. **Schema Errors**: Ensure tenant schemas are properly set up

### Logs
The scripts provide detailed logging. Check console output for:
- Creation confirmations
- Error messages
- Summary statistics

### Re-running Scripts
The scripts are designed to be idempotent:
- Won't create duplicate offices/users
- Won't create duplicate assignments
- Safe to run multiple times

## Integration

These scripts integrate with:
- **University Management**: Uses existing university tenants
- **KPI Definitions**: Uses existing KPIs and academic years
- **Office Management**: Creates office hierarchy
- **User Management**: Creates users with profiles
- **Assignment System**: Creates realistic assignment structure

## Next Steps

After seeding:
1. **Test Login**: Verify users can log in (default password: "password123")
2. **Check Assignments**: View assignments in the web interface
3. **Create Reports**: Test KPI reporting functionality
4. **Customize Data**: Modify scripts for your specific needs
