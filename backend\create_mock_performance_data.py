"""
<PERSON><PERSON><PERSON> to create mock performance data for dashboard demonstration.
This script generates realistic KPI reports and performance data for all universities.
"""

import os
import sys
import django
import logging
import random
from decimal import Decimal
from datetime import date, timedelta, datetime

# Add the project directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'moe_kpi_tracker.settings')
django.setup()

# Import after Django setup
from django.contrib.auth.models import User
from django.db import transaction
from django_tenants.utils import schema_context
from universities.models import University
from offices.models import Office
from kpi_definitions.models import KPI, AcademicYear
from kpi_assignments.models import (
    UniversityKPIAssignment, OfficeKPIAssignment, UserKPIAssignment,
    KPIReport, OfficeKPIReport, UserKPIReport
)

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def get_current_academic_year():
    """Get the current academic year."""
    try:
        return AcademicYear.objects.filter(is_current=True).first()
    except AcademicYear.DoesNotExist:
        return None

def generate_realistic_value(kpi, target, base_performance=80):
    """Generate a realistic value for a KPI based on its characteristics."""
    if not target:
        target = Decimal('100.0')
    
    # Add some randomness based on university performance level
    variation = random.uniform(0.7, 1.3)  # ±30% variation
    performance_factor = base_performance / 100.0
    
    if kpi.direction == 'increase':
        # For increase KPIs, generate values around the target with some achieving above target
        base_value = float(target) * performance_factor * variation
        return Decimal(str(max(0, base_value)))
    
    elif kpi.direction == 'decrease':
        # For decrease KPIs, lower values are better
        base_value = float(target) * (2 - performance_factor) * variation
        return Decimal(str(max(0, base_value)))
    
    else:  # maintain
        # For maintain KPIs, stay close to target
        base_value = float(target) * (0.9 + 0.2 * performance_factor) * variation
        return Decimal(str(max(0, base_value)))

def create_university_reports(university, academic_year, base_performance):
    """Create KPI reports for a university."""
    logger.info(f"Creating reports for {university.name} (base performance: {base_performance}%)")
    
    reports_created = 0
    
    with schema_context(university.schema_name):
        # Get university KPI assignments
        assignments = UniversityKPIAssignment.objects.filter(
            university=university,
            academic_year=academic_year,
            is_active=True
        )
        
        for assignment in assignments:
            # Check if report already exists
            if KPIReport.objects.filter(university_assignment=assignment).exists():
                continue
            
            # Generate realistic value
            value = generate_realistic_value(assignment.kpi, assignment.target, base_performance)
            
            # Create report
            report = KPIReport.objects.create(
                university_assignment=assignment,
                value=value,
                reporting_period=date.today().replace(day=1).strftime('%Y-%m'),  # YYYY-MM format
                reported_by=User.objects.filter(is_staff=True).first(),
                reported_at=datetime.now(),
                status='approved',
                notes=f"Mock data for {assignment.kpi.title}",
                supporting_document=None
            )
            
            reports_created += 1
            
            # Create office reports for this KPI
            office_assignments = OfficeKPIAssignment.objects.filter(
                university_assignment=assignment,
                is_active=True
            )
            
            for office_assignment in office_assignments:
                if OfficeKPIReport.objects.filter(office_assignment=office_assignment).exists():
                    continue
                
                # Office performance varies around university performance
                office_variation = random.uniform(0.8, 1.2)
                office_value = value * Decimal(str(office_variation))
                
                OfficeKPIReport.objects.create(
                    office_assignment=office_assignment,
                    value=office_value,
                    reporting_period=date.today().replace(day=1).strftime('%Y-%m'),
                    reported_by=User.objects.filter(is_staff=True).first(),
                    reported_at=datetime.now(),
                    status='approved',
                    notes=f"Mock office data for {assignment.kpi.title}",
                    supporting_document=None
                )
                
                # Create user reports for this office KPI
                user_assignments = UserKPIAssignment.objects.filter(
                    office_assignment=office_assignment,
                    is_active=True
                )
                
                for user_assignment in user_assignments:
                    if UserKPIReport.objects.filter(user_assignment=user_assignment).exists():
                        continue
                    
                    # User performance varies around office performance
                    user_variation = random.uniform(0.7, 1.3)
                    user_value = office_value * Decimal(str(user_variation)) * (user_assignment.weight / Decimal('100'))
                    
                    UserKPIReport.objects.create(
                        user_assignment=user_assignment,
                        value=user_value,
                        reporting_period=date.today().replace(day=1).strftime('%Y-%m'),
                        reported_at=datetime.now(),
                        status='approved',
                        notes=f"Mock user data for {assignment.kpi.title}",
                        supporting_document=None
                    )
    
    return reports_created

def create_historical_reports(university, academic_year, base_performance, months_back=6):
    """Create historical reports for trend analysis."""
    logger.info(f"Creating historical reports for {university.name}")
    
    with schema_context(university.schema_name):
        assignments = UniversityKPIAssignment.objects.filter(
            university=university,
            academic_year=academic_year,
            is_active=True
        )[:10]  # Limit to first 10 KPIs for performance
        
        for month_offset in range(1, months_back + 1):
            reporting_date = date.today().replace(day=1) - timedelta(days=30 * month_offset)
            
            # Gradually improve or decline performance over time
            trend_factor = random.choice([0.98, 0.99, 1.0, 1.01, 1.02])  # Slight trend
            historical_performance = base_performance * (trend_factor ** month_offset)
            
            for assignment in assignments:
                # Check if historical report exists
                if KPIReport.objects.filter(
                    university_assignment=assignment,
                    reporting_period=reporting_date
                ).exists():
                    continue
                
                value = generate_realistic_value(assignment.kpi, assignment.target, historical_performance)
                
                KPIReport.objects.create(
                    university_assignment=assignment,
                    value=value,
                    reporting_period=reporting_date.strftime('%Y-%m'),
                    reported_by=User.objects.filter(is_staff=True).first(),
                    reported_at=datetime.now() - timedelta(days=30 * month_offset),
                    status='approved',
                    notes=f"Historical mock data for {assignment.kpi.title}",
                    supporting_document=None
                )

def main():
    """Main function to create mock performance data."""
    logger.info("Starting mock performance data creation...")
    
    # Get current academic year
    academic_year = get_current_academic_year()
    if not academic_year:
        logger.error("No current academic year found. Please create one first.")
        return
    
    # Define university performance levels (realistic distribution)
    university_performance = {
        'Addis Ababa University': 92,
        'Bahir Dar University': 88,
        'Jimma University': 85,
        'Hawassa University': 82,
        'Mekelle University': 79,
        'Haramaya University': 76,
        'Arba Minch University': 74,
        'University of Gondar': 71,
    }
    
    # Get all universities except public tenant
    universities = University.objects.exclude(schema_name='public')
    
    total_reports = 0
    
    for university in universities:
        try:
            with transaction.atomic():
                # Get base performance for this university
                base_performance = university_performance.get(university.name, 80)
                
                # Create current reports
                reports_count = create_university_reports(university, academic_year, base_performance)
                total_reports += reports_count
                
                # Create historical reports for trends
                create_historical_reports(university, academic_year, base_performance)
                
                logger.info(f"Created {reports_count} current reports for {university.name}")
                
        except Exception as e:
            logger.error(f"Error creating reports for {university.name}: {str(e)}")
            continue
    
    logger.info(f"Mock performance data creation completed. Total reports created: {total_reports}")
    
    # Print summary
    logger.info("\n=== Performance Summary ===")
    for university in universities:
        base_performance = university_performance.get(university.name, 80)
        status = "Excellent" if base_performance >= 85 else "Good" if base_performance >= 75 else "Needs Attention"
        logger.info(f"{university.name}: {base_performance}% - {status}")

if __name__ == "__main__":
    main()
