import api from './api'

/**
 * Get comprehensive performance dashboard data
 * @returns {Promise<Object>} Performance dashboard data
 */
export const getPerformanceDashboard = async () => {
  try {
    const response = await api.get('/analytics/performance-dashboard/')
    return response.data
  } catch (error) {
    console.error('Error fetching performance dashboard:', error)
    throw error
  }
}

/**
 * Get benchmarking data across universities
 * @returns {Promise<Object>} Benchmarking data
 */
export const getBenchmarkingData = async () => {
  try {
    const response = await api.get('/analytics/benchmarking-data/')
    return response.data
  } catch (error) {
    console.error('Error fetching benchmarking data:', error)
    throw error
  }
}

/**
 * Get strategic insights and recommendations
 * @returns {Promise<Object>} Strategic insights
 */
export const getStrategicInsights = async () => {
  try {
    const response = await api.get('/analytics/strategic-insights/')
    return response.data
  } catch (error) {
    console.error('Error fetching strategic insights:', error)
    throw error
  }
}

/**
 * Get university-specific performance data
 * @param {string} universitySchema - University schema name
 * @returns {Promise<Object>} University performance data
 */
export const getUniversityPerformance = async (universitySchema) => {
  try {
    const response = await api.get(`/analytics/university-performance/${universitySchema}/`)
    return response.data
  } catch (error) {
    console.error(`Error fetching performance for ${universitySchema}:`, error)
    throw error
  }
}

/**
 * Get theme-specific performance across all universities
 * @param {number} themeId - Theme ID
 * @returns {Promise<Object>} Theme performance data
 */
export const getThemePerformance = async (themeId) => {
  try {
    const response = await api.get(`/analytics/theme-performance/${themeId}/`)
    return response.data
  } catch (error) {
    console.error(`Error fetching theme performance for ${themeId}:`, error)
    throw error
  }
}

/**
 * Generate mock data for demonstration when API is not available
 * @returns {Object} Mock dashboard data
 */
export const generateMockDashboardData = () => {
  const universities = [
    'Addis Ababa University', 'Bahir Dar University', 'Jimma University',
    'Hawassa University', 'Mekelle University', 'Haramaya University',
    'Arba Minch University', 'University of Gondar'
  ]

  const themes = [
    { name: 'Academic Excellence', color: '#1976d2' },
    { name: 'Research & Innovation', color: '#2e7d32' },
    { name: 'Student Success', color: '#ed6c02' },
    { name: 'Infrastructure', color: '#9c27b0' },
    { name: 'Financial Management', color: '#d32f2f' },
    { name: 'Human Resources', color: '#0288d1' }
  ]

  const getRandomPerformance = () => Math.floor(Math.random() * 30) + 70 // 70-100%
  const getRandomTrend = () => ['up', 'down', 'stable'][Math.floor(Math.random() * 3)]
  const getStatus = (performance) => {
    if (performance >= 85) return 'Excellent'
    if (performance >= 75) return 'Good'
    return 'Needs Attention'
  }

  const universityPerformance = universities.map((name, index) => {
    const performance = getRandomPerformance()
    return {
      name,
      performance,
      status: getStatus(performance),
      kpis: Math.floor(Math.random() * 20) + 25, // 25-45 KPIs
      offices: Math.floor(Math.random() * 15) + 12, // 12-27 offices
      trend: getRandomTrend(),
      reports_submitted: Math.floor(Math.random() * 100) + 50,
      completion_rate: Math.floor(Math.random() * 40) + 60,
      weighted_performance: performance
    }
  })

  const themePerformance = themes.map((theme, index) => ({
    ...theme,
    performance: getRandomPerformance(),
    universities: universities.length,
    kpis: Math.floor(Math.random() * 15) + 10, // 10-25 KPIs per theme
    total_weight: Math.floor(Math.random() * 200) + 100,
    avg_performance: getRandomPerformance()
  }))

  const systemOverview = {
    universities: universities.length,
    total_kpis: 156,
    total_assignments: 1131,
    total_reports: 2433,
    avg_kpis_per_university: Math.floor(1131 / universities.length)
  }

  const systemHealth = {
    performance_tracking: 'ACTIVE',
    real_time_monitoring: 'ENABLED',
    benchmarking: 'OPERATIONAL',
    strategic_planning: 'DATA_DRIVEN',
    accountability: 'TRANSPARENT'
  }

  return {
    system_overview: systemOverview,
    university_performance: universityPerformance,
    theme_performance: themePerformance,
    system_health: systemHealth,
    last_updated: new Date().toISOString(),
    performance_trends: generatePerformanceTrends(),
    alerts: generateAlerts(universityPerformance),
    recommendations: generateRecommendations(themePerformance)
  }
}

/**
 * Generate mock performance trends data
 * @returns {Object} Performance trends
 */
const generatePerformanceTrends = () => {
  const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun']
  const overallTrend = months.map(() => Math.floor(Math.random() * 10) + 75)
  
  return {
    months,
    overall_performance: overallTrend,
    university_trends: {
      'Addis Ababa University': months.map(() => Math.floor(Math.random() * 10) + 85),
      'Bahir Dar University': months.map(() => Math.floor(Math.random() * 10) + 80),
      'Jimma University': months.map(() => Math.floor(Math.random() * 10) + 78)
    }
  }
}

/**
 * Generate mock alerts
 * @param {Array} universityPerformance - University performance data
 * @returns {Array} Alerts
 */
const generateAlerts = (universityPerformance) => {
  const alerts = []
  
  universityPerformance.forEach(uni => {
    if (uni.performance < 75) {
      alerts.push({
        type: 'warning',
        title: `${uni.name} Performance Alert`,
        message: `Performance has dropped to ${uni.performance}%. Immediate attention required.`,
        priority: 'high',
        timestamp: new Date().toISOString()
      })
    }
    
    if (uni.completion_rate < 70) {
      alerts.push({
        type: 'info',
        title: `${uni.name} Reporting Alert`,
        message: `Report completion rate is ${uni.completion_rate}%. Follow up needed.`,
        priority: 'medium',
        timestamp: new Date().toISOString()
      })
    }
  })
  
  return alerts
}

/**
 * Generate mock recommendations
 * @param {Array} themePerformance - Theme performance data
 * @returns {Array} Recommendations
 */
const generateRecommendations = (themePerformance) => {
  const recommendations = []
  
  themePerformance.forEach(theme => {
    if (theme.performance < 80) {
      recommendations.push({
        theme: theme.name,
        priority: theme.performance < 70 ? 'high' : 'medium',
        recommendation: `Focus on improving ${theme.name} metrics through targeted interventions and resource allocation.`,
        expected_impact: 'High',
        timeline: '3-6 months'
      })
    }
  })
  
  return recommendations
}

/**
 * Get comprehensive analytics with fallback to mock data
 * @returns {Promise<Object>} Analytics data
 */
export const getComprehensiveAnalytics = async () => {
  try {
    // Try to fetch real data first
    const data = await getPerformanceDashboard()
    return data
  } catch (error) {
    console.warn('API not available, using mock data:', error)
    // Return mock data if API fails
    return generateMockDashboardData()
  }
}

export default {
  getPerformanceDashboard,
  getBenchmarkingData,
  getStrategicInsights,
  getUniversityPerformance,
  getThemePerformance,
  generateMockDashboardData,
  getComprehensiveAnalytics
}
