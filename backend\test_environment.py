"""
Test script to check the environment before running seeding.
"""

import os
import sys
import django

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'moe_kpi_tracker.settings')
django.setup()

from universities.models import University
from kpi_definitions.models import AcademicYear, KPI
from kpi_assignments.models import UniversityKPIAssignment

def main():
    print("=== Environment Check ===")
    
    # Check universities
    universities = University.objects.exclude(schema_name='public')
    print(f"Universities (excluding public): {universities.count()}")
    for uni in universities:
        print(f"  - {uni.name} ({uni.schema_name})")
    
    # Check academic years
    academic_years = AcademicYear.objects.all()
    print(f"Academic years: {academic_years.count()}")
    for year in academic_years:
        print(f"  - {year.name} (Current: {year.is_current})")
    
    # Check KPIs
    kpis = KPI.objects.all()
    print(f"KPIs: {kpis.count()}")
    
    # Check university assignments
    uni_assignments = UniversityKPIAssignment.objects.all()
    print(f"University KPI assignments: {uni_assignments.count()}")
    
    if universities.count() == 0:
        print("WARNING: No universities found. Please create universities first.")
    
    if academic_years.count() == 0:
        print("WARNING: No academic years found. The script will create one.")
    
    if kpis.count() == 0:
        print("WARNING: No KPIs found. Please create KPIs first.")
    
    if uni_assignments.count() == 0:
        print("WARNING: No university KPI assignments found. Please create university assignments first.")
    
    print("Environment check completed.")

if __name__ == "__main__":
    main()
