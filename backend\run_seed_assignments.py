"""
Simple script to run the seeding process and display results.
"""

import os
import sys
import django
import logging

# Add the project directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'moe_kpi_tracker.settings')
django.setup()

# Import after Django setup
from django.contrib.auth.models import User
from django_tenants.utils import schema_context
from universities.models import University
from offices.models import Office, UserProfile
from kpi_assignments.models import UniversityKPIAssignment, OfficeKPIAssignment, UserKPIAssignment

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_existing_data():
    """Check existing data before seeding."""
    logger.info("Checking existing data...")
    
    universities = University.objects.exclude(schema_name='public')
    
    for university in universities:
        with schema_context(university.schema_name):
            office_count = Office.objects.count()
            user_count = User.objects.count()
            office_assignment_count = OfficeKPIAssignment.objects.count()
            user_assignment_count = UserKPIAssignment.objects.count()
            
            logger.info(f"{university.name}: {office_count} offices, {user_count} users, "
                       f"{office_assignment_count} office assignments, {user_assignment_count} user assignments")

def run_seeding():
    """Run the seeding process."""
    logger.info("Running seeding process...")
    
    try:
        # Import and run the seeding script
        from create_seed_users_and_assignments import main as seed_main
        seed_main()
        logger.info("Seeding completed successfully!")
        return True
    except Exception as e:
        logger.error(f"Error during seeding: {str(e)}")
        return False

def display_results():
    """Display results after seeding."""
    logger.info("Displaying results after seeding...")
    
    universities = University.objects.exclude(schema_name='public')
    
    for university in universities:
        with schema_context(university.schema_name):
            # Count offices and users
            office_count = Office.objects.count()
            user_count = User.objects.count()
            
            # Count assignments
            office_assignment_count = OfficeKPIAssignment.objects.count()
            user_assignment_count = UserKPIAssignment.objects.count()
            
            # Get sample data
            sample_offices = Office.objects.all()[:3]
            sample_users = User.objects.all()[:3]
            
            logger.info(f"\n=== {university.name} ===")
            logger.info(f"Offices: {office_count}")
            logger.info(f"Users: {user_count}")
            logger.info(f"Office KPI Assignments: {office_assignment_count}")
            logger.info(f"User KPI Assignments: {user_assignment_count}")
            
            if sample_offices:
                logger.info("Sample offices:")
                for office in sample_offices:
                    manager_name = office.manager.get_full_name() if office.manager else "No manager"
                    logger.info(f"  - {office.name} (Manager: {manager_name})")
            
            if sample_users:
                logger.info("Sample users:")
                for user in sample_users:
                    try:
                        profile = user.profile
                        office_name = profile.office.name if profile.office else "No office"
                        position = profile.position or "No position"
                        logger.info(f"  - {user.username} ({user.get_full_name()}) - {position} at {office_name}")
                    except:
                        logger.info(f"  - {user.username} ({user.get_full_name()}) - No profile")

def main():
    """Main function."""
    logger.info("=== Seed User and Office KPI Assignments ===")
    
    # Check existing data
    check_existing_data()
    
    # Run seeding
    success = run_seeding()
    
    if success:
        # Display results
        display_results()
    else:
        logger.error("Seeding failed. Please check the logs for errors.")

if __name__ == "__main__":
    main()
