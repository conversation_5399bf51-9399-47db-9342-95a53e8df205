from django.contrib.auth.backends import ModelBackend
from django.contrib.auth import get_user_model
from django.db import connection
from django_tenants.utils import schema_context, get_tenant_model, get_public_schema_name
import logging
import traceback

logger = logging.getLogger(__name__)
User = get_user_model()
TenantModel = get_tenant_model()

class MultiTenantAuthBackend(ModelBackend):
    """
    Custom authentication backend that checks all schemas for the user.
    """

    def authenticate(self, request, username=None, password=None, **kwargs):
        """
        Authenticate a user across all schemas.

        First tries to authenticate in the current schema.
        If that fails, tries all other schemas.
        """
        # Log the current schema and request info
        current_schema = connection.schema_name
        request_path = request.path if request else 'No request'
        logger.debug(f"MultiTenantAuthBackend: Authenticating user {username} in current schema {current_schema} for path {request_path}")

        # First try to authenticate in the current schema
        user = self._authenticate_user(username, password)
        if user:
            logger.debug(f"MultiTenantAuthBackend: User {username} authenticated in current schema {current_schema}")
            # Store the tenant information in the user object for later use
            user.tenant_schema = current_schema
            # Try to get the university information
            try:
                university = TenantModel.objects.get(schema_name=current_schema)
                user.tenant_university = university
            except TenantModel.DoesNotExist:
                user.tenant_university = None
            return user

        # If that fails, try all other schemas
        logger.debug(f"MultiTenantAuthBackend: User {username} not found in current schema {current_schema}, trying other schemas")

        # Get all tenants
        tenants = TenantModel.objects.exclude(schema_name=current_schema)
        logger.debug(f"MultiTenantAuthBackend: Found {tenants.count()} other schemas to check")

        # Try each tenant schema
        for tenant in tenants:
            logger.debug(f"MultiTenantAuthBackend: Trying schema {tenant.schema_name}")
            with schema_context(tenant.schema_name):
                user = self._authenticate_user(username, password)
                if user:
                    logger.debug(f"MultiTenantAuthBackend: User {username} authenticated in schema {tenant.schema_name}")
                    # Set the connection schema to the tenant's schema
                    connection.set_tenant(tenant)
                    logger.debug(f"MultiTenantAuthBackend: Set connection schema to {connection.schema_name}")
                    # Store the tenant information in the user object for later use
                    user.tenant_schema = tenant.schema_name
                    user.tenant_university = tenant
                    return user

        # If we get here, the user was not found in any schema
        # Make sure we're back to the original schema
        try:
            original_tenant = TenantModel.objects.get(schema_name=current_schema)
            connection.set_tenant(original_tenant)
            logger.debug(f"MultiTenantAuthBackend: Reset connection to original schema {connection.schema_name}")
        except TenantModel.DoesNotExist:
            # If the original tenant doesn't exist, log an error
            # Do NOT fall back to public schema - this is a security issue
            logger.error(f"MultiTenantAuthBackend: Original tenant {current_schema} not found and no fallback allowed")

        # If we get here, the user was not found in any schema
        logger.debug(f"MultiTenantAuthBackend: User {username} not found in any schema")
        return None

    def _authenticate_user(self, username, password):
        """
        Authenticate a user in the current schema.
        """
        if not username or not password:
            logger.debug(f"MultiTenantAuthBackend: Username or password is empty")
            return None

        try:
            # First try to find user by username
            user = User.objects.filter(username=username).first()

            # If not found by username, try by email
            if not user:
                user = User.objects.filter(email=username).first()
                if user:
                    logger.debug(f"MultiTenantAuthBackend: User found by email {username} in schema {connection.schema_name}")
                else:
                    logger.debug(f"MultiTenantAuthBackend: User {username} not found by username or email in schema {connection.schema_name}")
                    # List all users in the schema for debugging
                    all_users = User.objects.all()
                    logger.debug(f"MultiTenantAuthBackend: Users in schema {connection.schema_name}: {', '.join([u.username for u in all_users])}")
                    return None
            else:
                logger.debug(f"MultiTenantAuthBackend: User {username} found by username in schema {connection.schema_name}")

            logger.debug(f"MultiTenantAuthBackend: User details - ID: {user.id}, Username: {user.username}, Email: {user.email}, Is active: {user.is_active}, Is staff: {user.is_staff}")

            # Check if user is active
            if not user.is_active:
                logger.debug(f"MultiTenantAuthBackend: User {username} is not active")
                return None

            # Check password
            if user.check_password(password):
                logger.debug(f"MultiTenantAuthBackend: Password check successful for user {username}")
                return user
            else:
                logger.debug(f"MultiTenantAuthBackend: Password check failed for user {username}")
                return None
        except User.DoesNotExist:
            logger.debug(f"MultiTenantAuthBackend: User {username} does not exist in schema {connection.schema_name}")
            # Run the default password hasher once to reduce timing attacks
            User().set_password(password)
        except Exception as e:
            logger.error(f"MultiTenantAuthBackend: Error authenticating user {username}: {str(e)}")
            logger.error(traceback.format_exc())

        return None
