import { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, Link as RouterLink, useNavigate } from 'react-router-dom'
import {
  Container,
  Paper,
  Typography,
  Box,
  Button,
  Grid,
  Divider,
  Chip,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Card,
  CardContent,

  CircularProgress,
  Alert,
  Breadcrumbs,
  Link,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Avatar,
  Tooltip,
  LinearProgress,
  useTheme,
  alpha,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  Snackbar,
} from '@mui/material'
import {
  Edit as EditIcon,
  School as SchoolIcon,
  BarChart as BarChartIcon,
  Timeline as TimelineIcon,

  Speed as SpeedIcon,
  Assignment as AssignmentIcon,
  Add as AddIcon,
  Home as HomeIcon,
  NavigateNext as NavigateNextIcon,
  Event as CalendarIcon,
  Category as CategoryIcon,
  Description as DescriptionIcon,
  Straighten as StraightenIcon,
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  TrendingFlat as TrendingFlatIcon,
  Repeat as RepeatIcon,
  Source as SourceIcon,
  Info as InfoIcon,
  Person as PersonIcon,
  Email as EmailIcon,
  Business as BusinessIcon,

  VerifiedUser as VerifiedUserIcon,
  Delete as DeleteIcon,
  Warning as WarningIcon,
  FileDownload as FileDownloadIcon,
} from '@mui/icons-material'
import { getKPIById, getKPIAssignments, getKPIPerformance, deleteKPI, exportKPIsToExcel, exportKPIsToPDF } from '../../../services/kpiService'
import { formatDate } from '../../../utils/dateUtils'
import KPIPerformance from '../../kpi-definitions/KPIPerformance.jsx'

// Helper function to determine text color based on background color
const getContrastText = (hexColor) => {
  // Default to black if no color is provided
  if (!hexColor) return '#000000'

  // Remove the # if it exists
  const color = hexColor.charAt(0) === '#' ? hexColor.substring(1, 7) : hexColor

  // Convert hex to RGB
  const r = parseInt(color.substring(0, 2), 16)
  const g = parseInt(color.substring(2, 4), 16)
  const b = parseInt(color.substring(4, 6), 16)

  // Calculate luminance
  const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255

  // Return white for dark colors, black for light colors
  return luminance > 0.5 ? '#000000' : '#ffffff'
}

const KPIDetail = () => {
  // All hooks must be called at the top level, before any conditional returns
  const { id } = useParams()
  const theme = useTheme()
  const navigate = useNavigate()
  const [kpi, setKpi] = useState(null)
  const [assignments, setAssignments] = useState([])
  const [statistics, setStatistics] = useState({
    universityCount: 0,
    activeAssignments: 0,
    averageTarget: 'N/A',
    completionRate: 0
  })
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [deleteInProgress, setDeleteInProgress] = useState(false)
  const [snackbarOpen, setSnackbarOpen] = useState(false)
  const [snackbarMessage, setSnackbarMessage] = useState('')

  // Helper function to get direction icon - defined outside of render to avoid recreation
  const getDirectionIcon = (direction) => {
    switch(direction) {
      case 'increase':
        return <TrendingUpIcon color="success" />;
      case 'decrease':
        return <TrendingDownIcon color="error" />;
      case 'maintain':
        return <TrendingFlatIcon color="info" />;
      case 'range':
        return <StraightenIcon color="warning" />;
      default:
        return <TrendingUpIcon color="action" />;
    }
  };

  // Helper function to get status color - defined outside of render to avoid recreation
  const getStatusColor = (status) => {
    if (!theme) return '#999'; // Fallback color if theme is not available

    switch(status) {
      case 'active':
        return theme.palette.success.main;
      case 'draft':
        return theme.palette.grey[500];
      case 'under_review':
        return theme.palette.warning.main;
      case 'approved':
        return theme.palette.primary.main;
      case 'archived':
        return theme.palette.secondary.main;
      default:
        return theme.palette.error.main;
    }
  };

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true)
        console.log(`Fetching data for KPI ${id}...`)

        // First, get the KPI details
        const kpiData = await getKPIById(id)
        setKpi(kpiData)
        console.log('KPI data loaded successfully:', kpiData)

        // Initialize statistics with any data available from the KPI object
        let statsFromKPI = {
          universityCount: kpiData.university_count || 0,
          activeAssignments: kpiData.active_assignments || 0,
          averageTarget: kpiData.average_target || 'N/A',
          completionRate: kpiData.completion_rate || 0
        }

        console.log('Initial statistics from KPI data:', statsFromKPI)

        // Get assignments data
        let assignmentsData = []
        try {
          console.log('Fetching KPI assignments...')
          assignmentsData = await getKPIAssignments(id)

          // Process assignments to ensure we have unique universities
          if (assignmentsData && assignmentsData.length > 0) {
            console.log(`Loaded ${assignmentsData.length} raw assignments for KPI ${id}`)

            // Create a map to deduplicate by university ID
            const uniqueUniversityMap = new Map();

            assignmentsData.forEach(assignment => {
              const universityId = assignment.university?.id || assignment.university_id;

              // Only add if we don't already have this university or if this is a newer assignment
              if (universityId && (!uniqueUniversityMap.has(universityId) ||
                  new Date(assignment.updated_at) > new Date(uniqueUniversityMap.get(universityId).updated_at))) {
                uniqueUniversityMap.set(universityId, assignment);
              }
            });

            // Convert map back to array
            assignmentsData = Array.from(uniqueUniversityMap.values());
            console.log(`Processed to ${assignmentsData.length} unique university assignments for KPI ${id}`)
          }

          setAssignments(assignmentsData)

          if (assignmentsData.length > 0) {
            // Calculate statistics from real assignment data
            const activeAssignments = assignmentsData.filter(a => a.status === 'active').length
            console.log(`Active assignments: ${activeAssignments}`)

            // Calculate average target if we have assignments with target values
            let avgTarget = 'N/A'
            const assignmentsWithTargets = assignmentsData.filter(a =>
              (a.target_value !== undefined && a.target_value !== null) ||
              (a.target !== undefined && a.target !== null)
            )

            if (assignmentsWithTargets.length > 0) {
              const sum = assignmentsWithTargets.reduce((total, a) => {
                const targetValue = parseFloat(a.target_value || a.target || 0)
                return isNaN(targetValue) ? total : total + targetValue
              }, 0)

              avgTarget = (sum / assignmentsWithTargets.length).toFixed(2)
              if (kpiData.measurement_unit?.symbol || kpiData.measurement_unit_symbol) {
                avgTarget += ` ${kpiData.measurement_unit?.symbol || kpiData.measurement_unit_symbol}`
              }
              console.log(`Calculated average target: ${avgTarget}`)
            }

            // Update statistics from assignments
            statsFromKPI = {
              ...statsFromKPI,
              universityCount: assignmentsData.length, // Use the actual count of unique universities
              activeAssignments,
              averageTarget: avgTarget
            }

            console.log('Updated statistics from assignments:', statsFromKPI)
          }
        } catch (assignmentErr) {
          console.warn(`Could not fetch assignments for KPI ${id}:`, assignmentErr)
          // Continue with existing statistics
        }

        // Try to get performance data for the most accurate statistics
        try {
          console.log('Fetching KPI performance data...')
          const performanceData = await getKPIPerformance(id)
          console.log('Performance data:', performanceData)

          if (performanceData && Object.keys(performanceData).length > 0) {
            // Update statistics with performance data (most authoritative source)
            statsFromKPI = {
              ...statsFromKPI,
              completionRate: performanceData.completion_rate !== undefined ? performanceData.completion_rate : statsFromKPI.completionRate,
              universityCount: performanceData.university_count !== undefined ? performanceData.university_count : statsFromKPI.universityCount,
              activeAssignments: performanceData.active_assignments !== undefined ? performanceData.active_assignments : statsFromKPI.activeAssignments,
              averageTarget: performanceData.average_target !== undefined ? performanceData.average_target : statsFromKPI.averageTarget
            }

            console.log('Final statistics with performance data:', statsFromKPI)
          }
        } catch (performanceErr) {
          console.warn(`Could not fetch performance data for KPI ${id}:`, performanceErr)
          // Continue with existing statistics
        }

        // Set the final statistics
        setStatistics(statsFromKPI)
        console.log('Final statistics set:', statsFromKPI)

        setError(null)
      } catch (err) {
        console.error('Error fetching KPI data:', err)
        setError('Failed to load KPI details. Please try again later.')
      } finally {
        setLoading(false)
      }
    }

    fetchData()
  }, [id])

  const handleDeleteClick = () => {
    setDeleteDialogOpen(true)
  }

  const handleDeleteCancel = () => {
    setDeleteDialogOpen(false)
  }

  const handleDeleteConfirm = async () => {
    try {
      setDeleteInProgress(true)
      await deleteKPI(id)
      setDeleteDialogOpen(false)
      setSnackbarMessage('KPI deleted successfully')
      setSnackbarOpen(true)
      // Navigate back to KPIs list after a short delay
      setTimeout(() => {
        navigate('/kpis')
      }, 1500)
    } catch (err) {
      console.error('Error deleting KPI:', err)
      setDeleteDialogOpen(false)
      setSnackbarMessage('Failed to delete KPI. Please try again later.')
      setSnackbarOpen(true)
      setDeleteInProgress(false)
    }
  }

  const handleSnackbarClose = () => {
    setSnackbarOpen(false)
  }

  const handleExport = async (format) => {
    try {
      // Export only this specific KPI
      const params = { kpi_id: id }

      // Call the appropriate export function based on format
      let blobData
      if (format === 'excel') {
        blobData = await exportKPIsToExcel(params)
      } else {
        blobData = await exportKPIsToPDF(params)
      }

      // Create a blob URL and trigger download
      const downloadUrl = window.URL.createObjectURL(blobData)

      // Create a temporary link and click it to trigger download
      const link = document.createElement('a')
      link.href = downloadUrl

      // Get tenant information if available
      const { tenant } = await import('../../../contexts/TenantContext').then(module => {
        return { tenant: module.useTenant().tenant };
      }).catch(() => {
        return { tenant: null };
      });

      // Set filename with tenant name if available
      const tenantPrefix = tenant?.name ? `${tenant.name.replace(/\s+/g, '_')}_` : ''
      const fileExtension = format === 'excel' ? 'xlsx' : 'pdf'
      link.download = `${tenantPrefix}KPI_${id}_${kpi.title.replace(/[^a-zA-Z0-9]/g, '_')}.${fileExtension}`

      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      // Clean up the URL
      window.URL.revokeObjectURL(downloadUrl)

      setSnackbarMessage(`KPI exported to ${format.toUpperCase()} successfully`)
      setSnackbarOpen(true)
    } catch (err) {
      console.error(`Error exporting KPI to ${format}:`, err)
      setSnackbarMessage(`Failed to export KPI to ${format.toUpperCase()}. Please try again later.`)
      setSnackbarOpen(true)
    }
  }

  const handleExportExcel = () => handleExport('excel')
  const handleExportPDF = () => handleExport('pdf')

  // Render loading state
  if (loading) {
    return (
      <Container maxWidth="xl" sx={{ mt: 4, mb: 4 }}>
        <Box sx={{
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center',
          my: 8
        }}>
          <CircularProgress size={60} thickness={4} />
          <Typography variant="h6" sx={{ mt: 3, fontFamily: '"Cinzel", serif' }}>
            Loading KPI Details...
          </Typography>
        </Box>
      </Container>
    )
  }

  // Render error state
  if (error) {
    return (
      <Container maxWidth="xl" sx={{ mt: 4, mb: 4 }}>
        <Alert
          severity="error"
          sx={{
            mb: 3,
            p: 2,
            borderRadius: 2,
            '& .MuiAlert-icon': {
              fontSize: '2rem'
            }
          }}
        >
          {error}
        </Alert>
        <Button
          component={RouterLink}
          to="/kpis"
          variant="contained"
          color="primary"
          sx={{
            py: 1,
            px: 3,
            background: 'linear-gradient(45deg, #7e57c2 30%, #9575cd 90%)',
            boxShadow: '0 4px 10px rgba(126, 87, 194, 0.3)',
            '&:hover': {
              boxShadow: '0 6px 15px rgba(126, 87, 194, 0.4)',
              transform: 'translateY(-2px)',
            },
          }}
        >
          Back to KPIs
        </Button>
      </Container>
    )
  }

  // Render not found state
  if (!kpi) {
    return (
      <Container maxWidth="xl" sx={{ mt: 4, mb: 4 }}>
        <Alert
          severity="warning"
          sx={{
            mb: 3,
            p: 2,
            borderRadius: 2,
            '& .MuiAlert-icon': {
              fontSize: '2rem'
            }
          }}
        >
          KPI not found.
        </Alert>
        <Button
          component={RouterLink}
          to="/kpis"
          variant="contained"
          color="primary"
          sx={{
            py: 1,
            px: 3,
            background: 'linear-gradient(45deg, #7e57c2 30%, #9575cd 90%)',
            boxShadow: '0 4px 10px rgba(126, 87, 194, 0.3)',
            '&:hover': {
              boxShadow: '0 6px 15px rgba(126, 87, 194, 0.4)',
              transform: 'translateY(-2px)',
            },
          }}
        >
          Back to KPIs
        </Button>
      </Container>
    )
  }

  return (
    <Container maxWidth="xl" sx={{ mt: 4, mb: 4 }}>
      <Breadcrumbs
        separator={<NavigateNextIcon fontSize="small" />}
        aria-label="breadcrumb"
        sx={{ mb: 3 }}
      >
        <Link
          component={RouterLink}
          to="/dashboard"
          color="inherit"
          sx={{ display: 'flex', alignItems: 'center' }}
        >
          <HomeIcon sx={{ mr: 0.5 }} fontSize="inherit" />
          Dashboard
        </Link>
        <Link
          component={RouterLink}
          to="/kpis"
          color="inherit"
          sx={{ display: 'flex', alignItems: 'center' }}
        >
          <BarChartIcon sx={{ mr: 0.5 }} fontSize="inherit" />
          KPIs
        </Link>
        <Typography color="text.primary">{kpi.title}</Typography>
      </Breadcrumbs>

      {/* KPI Performance Component */}
      <KPIPerformance kpiId={id} />

      <Paper
        elevation={3}
        sx={{
          mb: 3,
          position: 'relative',
          overflow: 'hidden',
          borderRadius: 2,
          background: `linear-gradient(to right, ${alpha(theme.palette.primary.main, 0.05)}, ${alpha(theme.palette.primary.light, 0.1)})`,
          boxShadow: `0 4px 20px ${alpha(theme.palette.primary.main, 0.15)}`,
          transition: 'transform 0.3s, box-shadow 0.3s',
          '&:hover': {
            boxShadow: `0 8px 30px ${alpha(theme.palette.primary.main, 0.2)}`,
            transform: 'translateY(-5px)'
          }
        }}
      >
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            p: 3,
            position: 'relative',
          }}
        >
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Avatar
              sx={{
                bgcolor: alpha(theme.palette.primary.main, 0.1),
                color: theme.palette.primary.main,
                width: 70,
                height: 70,
                mr: 3,
                boxShadow: `0 0 0 1px ${alpha(theme.palette.primary.main, 0.2)}, 0 4px 10px ${alpha(theme.palette.primary.main, 0.2)}`,
                transition: 'transform 0.3s',
                '&:hover': {
                  transform: 'scale(1.05) rotate(5deg)'
                }
              }}
            >
              <BarChartIcon sx={{ fontSize: 35 }} />
            </Avatar>
            <Box>
              <Typography
                variant="h4"
                component="h1"
                gutterBottom
                sx={{
                  fontWeight: 'bold',
                  background: `linear-gradient(45deg, ${theme.palette.primary.dark} 30%, ${theme.palette.primary.light} 90%)`,
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent',
                  mb: 0.5,
                  textShadow: `0 2px 4px ${alpha(theme.palette.primary.main, 0.1)}`,
                  letterSpacing: '-0.5px'
                }}
              >
                {kpi.title}
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, flexWrap: 'wrap' }}>
                <Chip
                  icon={getDirectionIcon(kpi.direction)}
                  label={kpi.direction.replace('_', ' ')}
                  size="small"
                  sx={{
                    bgcolor: alpha(theme.palette.info.main, 0.1),
                    color: theme.palette.info.main,
                    border: `1px solid ${alpha(theme.palette.info.main, 0.3)}`,
                    fontWeight: 'medium',
                    textTransform: 'capitalize',
                    transition: 'all 0.2s',
                    '&:hover': {
                      bgcolor: alpha(theme.palette.info.main, 0.2),
                      transform: 'translateY(-2px)'
                    }
                  }}
                />
                <Chip
                  icon={<InfoIcon fontSize="small" />}
                  label={kpi.status.replace('_', ' ')}
                  size="small"
                  sx={{
                    bgcolor: alpha(getStatusColor(kpi.status), 0.1),
                    color: getStatusColor(kpi.status),
                    border: `1px solid ${alpha(getStatusColor(kpi.status), 0.3)}`,
                    fontWeight: 'medium',
                    textTransform: 'capitalize',
                    transition: 'all 0.2s',
                    '&:hover': {
                      bgcolor: alpha(getStatusColor(kpi.status), 0.2),
                      transform: 'translateY(-2px)'
                    }
                  }}
                />

                {/* Display tags in the header */}
                {kpi.tags && kpi.tags.length > 0 && (
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, ml: 0.5 }}>
                    {kpi.tags.map((tag) => (
                      <Chip
                        key={tag.id}
                        label={tag.name}
                        size="small"
                        sx={{
                          bgcolor: tag.color || alpha(theme.palette.primary.main, 0.1),
                          color: tag.color ? getContrastText(tag.color) : theme.palette.text.primary,
                          border: `1px solid ${alpha(theme.palette.divider, 0.2)}`,
                          height: 24,
                          fontWeight: 'medium',
                          transition: 'all 0.2s',
                          '&:hover': {
                            transform: 'translateY(-2px)',
                            boxShadow: 1,
                            bgcolor: tag.color ? alpha(tag.color, 0.8) : alpha(theme.palette.primary.main, 0.2),
                          }
                        }}
                      />
                    ))}
                  </Box>
                )}
              </Box>
            </Box>
          </Box>
          <Box sx={{ display: 'flex', gap: 1 }}>
            <Button
              component={RouterLink}
              to={`/kpis/${kpi.id}/edit`}
              variant="contained"
              color="primary"
              startIcon={<EditIcon />}
              sx={{
                px: 3,
                py: 1,
                borderRadius: 2,
                background: `linear-gradient(45deg, ${theme.palette.primary.main} 30%, ${theme.palette.primary.light} 90%)`,
                boxShadow: `0 4px 10px ${alpha(theme.palette.primary.main, 0.3)}`,
                transition: 'all 0.3s',
                '&:hover': {
                  boxShadow: `0 6px 15px ${alpha(theme.palette.primary.main, 0.4)}`,
                  transform: 'translateY(-3px)'
                }
              }}
            >
              Edit KPI
            </Button>
            <Button
              variant="outlined"
              color="info"
              startIcon={<FileDownloadIcon />}
              onClick={handleExportExcel}
              sx={{
                px: 3,
                py: 1,
                borderRadius: 2,
                borderWidth: 2,
                '&:hover': {
                  borderWidth: 2,
                  bgcolor: alpha(theme.palette.info.main, 0.1),
                  transform: 'translateY(-3px)',
                  boxShadow: `0 4px 10px ${alpha(theme.palette.info.main, 0.2)}`
                }
              }}
            >
              Excel
            </Button>
            <Button
              variant="outlined"
              color="secondary"
              startIcon={<FileDownloadIcon />}
              onClick={handleExportPDF}
              sx={{
                px: 3,
                py: 1,
                borderRadius: 2,
                borderWidth: 2,
                '&:hover': {
                  borderWidth: 2,
                  bgcolor: alpha(theme.palette.secondary.main, 0.1),
                  transform: 'translateY(-3px)',
                  boxShadow: `0 4px 10px ${alpha(theme.palette.secondary.main, 0.2)}`
                }
              }}
            >
              PDF
            </Button>
            <Button
              variant="outlined"
              color="error"
              startIcon={<DeleteIcon />}
              onClick={handleDeleteClick}
              sx={{
                px: 3,
                py: 1,
                borderRadius: 2,
                borderWidth: 2,
                '&:hover': {
                  borderWidth: 2,
                  bgcolor: alpha(theme.palette.error.main, 0.1),
                  transform: 'translateY(-3px)',
                  boxShadow: `0 4px 10px ${alpha(theme.palette.error.main, 0.2)}`
                }
              }}
            >
              Delete
            </Button>
          </Box>
        </Box>
      </Paper>

      <Grid container spacing={3}>
        <Grid item xs={12} md={8}>
          <Paper
            sx={{
              p: 0,
              mb: 3,
              overflow: 'hidden',
              borderRadius: 2,
              boxShadow: 3,
              transition: 'transform 0.3s, box-shadow 0.3s',
              '&:hover': {
                boxShadow: 6,
                transform: 'translateY(-4px)'
              }
            }}
          >
            <Box
              sx={{
                bgcolor: alpha(theme.palette.primary.main, 0.1),
                p: 2,
                display: 'flex',
                alignItems: 'center',
                gap: 1.5,
                borderBottom: `1px solid ${alpha(theme.palette.primary.main, 0.2)}`
              }}
            >
              <Avatar
                sx={{
                  bgcolor: alpha(theme.palette.primary.main, 0.2),
                  color: theme.palette.primary.main,
                }}
              >
                <DescriptionIcon />
              </Avatar>
              <Typography
                variant="h6"
                sx={{
                  fontWeight: 'bold',
                  color: theme.palette.primary.main
                }}
              >
                KPI Details
              </Typography>
            </Box>

            <Box sx={{ p: 3 }}>
              <Grid container spacing={3}>
                {/* Description Section */}
                <Grid item xs={12}>
                  <Box
                    sx={{
                      p: 3,
                      borderRadius: 2,
                      bgcolor: alpha(theme.palette.background.default, 0.7),
                      border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
                      mb: 2,
                      boxShadow: `0 2px 8px ${alpha(theme.palette.primary.main, 0.05)}`,
                      transition: 'all 0.3s',
                      '&:hover': {
                        boxShadow: `0 4px 12px ${alpha(theme.palette.primary.main, 0.1)}`,
                        transform: 'translateY(-2px)',
                        bgcolor: alpha(theme.palette.background.paper, 0.9),
                      }
                    }}
                  >
                    <Box sx={{ display: 'flex', alignItems: 'flex-start', mb: 2 }}>
                      <Avatar
                        sx={{
                          mr: 2,
                          bgcolor: alpha(theme.palette.primary.main, 0.1),
                          color: theme.palette.primary.main,
                        }}
                      >
                        <DescriptionIcon />
                      </Avatar>
                      <Box>
                        <Typography variant="h6" fontWeight="bold" color="primary.main" gutterBottom>
                          Description
                        </Typography>
                        <Typography variant="body1" sx={{ lineHeight: 1.7, color: theme.palette.text.primary }}>
                          {kpi.description || 'No description provided.'}
                        </Typography>
                      </Box>
                    </Box>
                  </Box>
                </Grid>

                {/* Calculation Method Section */}
                {kpi.calculation_method && (
                  <Grid item xs={12}>
                    <Box
                      sx={{
                        p: 3,
                        borderRadius: 2,
                        bgcolor: alpha(theme.palette.background.default, 0.7),
                        border: `1px solid ${alpha(theme.palette.info.main, 0.1)}`,
                        mb: 2,
                        boxShadow: `0 2px 8px ${alpha(theme.palette.info.main, 0.05)}`,
                        transition: 'all 0.3s',
                        '&:hover': {
                          boxShadow: `0 4px 12px ${alpha(theme.palette.info.main, 0.1)}`,
                          transform: 'translateY(-2px)',
                          bgcolor: alpha(theme.palette.background.paper, 0.9),
                        }
                      }}
                    >
                      <Box sx={{ display: 'flex', alignItems: 'flex-start', mb: 2 }}>
                        <Avatar
                          sx={{
                            mr: 2,
                            bgcolor: alpha(theme.palette.info.main, 0.1),
                            color: theme.palette.info.main,
                          }}
                        >
                          <SpeedIcon />
                        </Avatar>
                        <Box>
                          <Typography variant="h6" fontWeight="bold" color="info.main" gutterBottom>
                            Calculation Method
                          </Typography>
                          <Typography variant="body1" sx={{ lineHeight: 1.7, color: theme.palette.text.primary }}>
                            {kpi.calculation_method || 'No calculation method provided.'}
                          </Typography>
                        </Box>
                      </Box>
                    </Box>
                  </Grid>
                )}

                {/* Creator Information */}
                {kpi.created_by_name && (
                  <Grid item xs={12}>
                    <Box
                      sx={{
                        p: 3,
                        borderRadius: 2,
                        bgcolor: alpha(theme.palette.background.default, 0.7),
                        border: `1px solid ${alpha(theme.palette.success.main, 0.1)}`,
                        mb: 2,
                        boxShadow: `0 2px 8px ${alpha(theme.palette.success.main, 0.05)}`,
                        transition: 'all 0.3s',
                        '&:hover': {
                          boxShadow: `0 4px 12px ${alpha(theme.palette.success.main, 0.1)}`,
                          transform: 'translateY(-2px)',
                          bgcolor: alpha(theme.palette.background.paper, 0.9),
                        }
                      }}
                    >
                      <Box sx={{ display: 'flex', alignItems: 'flex-start' }}>
                        <Avatar
                          sx={{
                            mr: 2,
                            bgcolor: alpha(theme.palette.success.main, 0.1),
                            color: theme.palette.success.main,
                          }}
                        >
                          <PersonIcon />
                        </Avatar>
                        <Box>
                          <Typography variant="h6" fontWeight="bold" color="success.main" gutterBottom>
                            Audit Information
                          </Typography>
                          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                              <Typography variant="body2" color="text.secondary">
                                Created by:
                              </Typography>
                              <Typography variant="body1" fontWeight="medium">
                                {kpi.created_by_name}
                              </Typography>
                              {kpi.created_at && (
                                <Typography variant="body2" color="text.secondary">
                                  on {new Date(kpi.created_at).toLocaleString()}
                                </Typography>
                              )}
                            </Box>

                            {kpi.approved_by_name && (
                              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                <Typography variant="body2" color="text.secondary">
                                  Approved by:
                                </Typography>
                                <Typography variant="body1" fontWeight="medium">
                                  {kpi.approved_by_name}
                                </Typography>
                                {kpi.approved_at && (
                                  <Typography variant="body2" color="text.secondary">
                                    on {new Date(kpi.approved_at).toLocaleString()}
                                  </Typography>
                                )}
                              </Box>
                            )}

                            {kpi.updated_at && (
                              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                <Typography variant="body2" color="text.secondary">
                                  Last updated:
                                </Typography>
                                <Typography variant="body1" fontWeight="medium">
                                  {new Date(kpi.updated_at).toLocaleString()}
                                </Typography>
                              </Box>
                            )}
                          </Box>
                        </Box>
                      </Box>
                    </Box>
                  </Grid>
                )}

              <Grid item xs={12} md={6}>
                <Card
                  sx={{
                    height: '100%',
                    display: 'flex',
                    flexDirection: 'column',
                    borderRadius: 2,
                    boxShadow: 2,
                    transition: 'transform 0.2s',
                    '&:hover': {
                      transform: 'translateY(-4px)',
                      boxShadow: 4
                    }
                  }}
                >
                  <Box
                    sx={{
                      p: 2,
                      bgcolor: alpha(theme.palette.primary.main, 0.05),
                      borderBottom: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
                      display: 'flex',
                      alignItems: 'center',
                      gap: 1
                    }}
                  >
                    <CategoryIcon color="primary" />
                    <Typography variant="subtitle1" fontWeight="bold">
                      Theme
                    </Typography>
                  </Box>
                  <CardContent sx={{ flexGrow: 1, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                    {kpi.theme && kpi.theme.id ? (
                      <Box sx={{ textAlign: 'center' }}>
                        <Avatar
                          sx={{
                            width: 60,
                            height: 60,
                            bgcolor: alpha(theme.palette.primary.main, 0.1),
                            color: theme.palette.primary.main,
                            mx: 'auto',
                            mb: 1
                          }}
                        >
                          {kpi.theme.name?.charAt(0) || 'T'}
                        </Avatar>
                        <Link
                          component={RouterLink}
                          to={`/themes/${kpi.theme.id}`}
                          color="primary"
                          sx={{
                            fontWeight: 'bold',
                            textDecoration: 'none',
                            '&:hover': { textDecoration: 'underline' }
                          }}
                        >
                          {kpi.theme.name || 'Unnamed Theme'}
                        </Link>
                      </Box>
                    ) : kpi.theme_name ? (
                      <Box sx={{ textAlign: 'center' }}>
                        <Avatar
                          sx={{
                            width: 60,
                            height: 60,
                            bgcolor: alpha(theme.palette.primary.main, 0.1),
                            color: theme.palette.primary.main,
                            mx: 'auto',
                            mb: 1
                          }}
                        >
                          {kpi.theme_name?.charAt(0) || 'T'}
                        </Avatar>
                        <Typography variant="body1" color="primary" fontWeight="medium">
                          {kpi.theme_name}
                        </Typography>
                      </Box>
                    ) : (
                      <Typography variant="body1" color="text.secondary">
                        Not specified
                      </Typography>
                    )}
                  </CardContent>
                </Card>
              </Grid>

              <Grid item xs={12} md={6}>
                <Card
                  sx={{
                    height: '100%',
                    display: 'flex',
                    flexDirection: 'column',
                    borderRadius: 2,
                    boxShadow: 2,
                    transition: 'transform 0.2s',
                    '&:hover': {
                      transform: 'translateY(-4px)',
                      boxShadow: 4
                    }
                  }}
                >
                  <Box
                    sx={{
                      p: 2,
                      bgcolor: alpha(theme.palette.secondary.main, 0.05),
                      borderBottom: `1px solid ${alpha(theme.palette.secondary.main, 0.1)}`,
                      display: 'flex',
                      alignItems: 'center',
                      gap: 1
                    }}
                  >
                    <TimelineIcon color="secondary" />
                    <Typography variant="subtitle1" fontWeight="bold">
                      SubTheme
                    </Typography>
                  </Box>
                  <CardContent sx={{ flexGrow: 1, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                    {kpi.sub_theme && kpi.sub_theme.id ? (
                      <Box sx={{ textAlign: 'center' }}>
                        <Avatar
                          sx={{
                            width: 60,
                            height: 60,
                            bgcolor: alpha(theme.palette.secondary.main, 0.1),
                            color: theme.palette.secondary.main,
                            mx: 'auto',
                            mb: 1
                          }}
                        >
                          {kpi.sub_theme.name?.charAt(0) || 'S'}
                        </Avatar>
                        <Link
                          component={RouterLink}
                          to={`/subthemes/${kpi.sub_theme.id}`}
                          color="secondary"
                          sx={{
                            fontWeight: 'bold',
                            textDecoration: 'none',
                            '&:hover': { textDecoration: 'underline' }
                          }}
                        >
                          {kpi.sub_theme.name || 'Unnamed Subtheme'}
                        </Link>
                      </Box>
                    ) : kpi.sub_theme_name ? (
                      <Box sx={{ textAlign: 'center' }}>
                        <Avatar
                          sx={{
                            width: 60,
                            height: 60,
                            bgcolor: alpha(theme.palette.secondary.main, 0.1),
                            color: theme.palette.secondary.main,
                            mx: 'auto',
                            mb: 1
                          }}
                        >
                          {kpi.sub_theme_name?.charAt(0) || 'S'}
                        </Avatar>
                        <Typography variant="body1" color="secondary" fontWeight="medium">
                          {kpi.sub_theme_name}
                        </Typography>
                      </Box>
                    ) : (
                      <Typography variant="body1" color="text.secondary">
                        Not specified
                      </Typography>
                    )}
                  </CardContent>
                </Card>
              </Grid>

              <Grid item xs={12} md={6}>
                <Card
                  sx={{
                    height: '100%',
                    display: 'flex',
                    flexDirection: 'column',
                    borderRadius: 2,
                    boxShadow: 2,
                    transition: 'transform 0.2s',
                    '&:hover': {
                      transform: 'translateY(-4px)',
                      boxShadow: 4
                    }
                  }}
                >
                  <Box
                    sx={{
                      p: 2,
                      bgcolor: alpha(theme.palette.info.main, 0.05),
                      borderBottom: `1px solid ${alpha(theme.palette.info.main, 0.1)}`,
                      display: 'flex',
                      alignItems: 'center',
                      gap: 1
                    }}
                  >
                    <StraightenIcon color="info" />
                    <Typography variant="subtitle1" fontWeight="bold">
                      Measurement Unit
                    </Typography>
                  </Box>
                  <CardContent sx={{ flexGrow: 1, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                    {kpi.measurement_unit && kpi.measurement_unit.name ? (
                      <Box sx={{ textAlign: 'center' }}>
                        <Avatar
                          sx={{
                            width: 60,
                            height: 60,
                            bgcolor: alpha(theme.palette.info.main, 0.1),
                            color: theme.palette.info.main,
                            mx: 'auto',
                            mb: 1,
                            fontWeight: 'bold',
                            fontSize: '1.5rem'
                          }}
                        >
                          {kpi.measurement_unit.symbol || '#'}
                        </Avatar>
                        <Typography variant="body1" color="info.main" fontWeight="medium">
                          {kpi.measurement_unit.name || 'N/A'}
                        </Typography>
                      </Box>
                    ) : kpi.measurement_unit_name ? (
                      <Box sx={{ textAlign: 'center' }}>
                        <Avatar
                          sx={{
                            width: 60,
                            height: 60,
                            bgcolor: alpha(theme.palette.info.main, 0.1),
                            color: theme.palette.info.main,
                            mx: 'auto',
                            mb: 1,
                            fontWeight: 'bold',
                            fontSize: '1.5rem'
                          }}
                        >
                          {kpi.measurement_unit_symbol || '#'}
                        </Avatar>
                        <Typography variant="body1" color="info.main" fontWeight="medium">
                          {kpi.measurement_unit_name || 'N/A'}
                        </Typography>
                      </Box>
                    ) : (
                      <Typography variant="body1" color="text.secondary">
                        Not specified
                      </Typography>
                    )}
                  </CardContent>
                </Card>
              </Grid>

              <Grid item xs={12} md={6}>
                <Card
                  sx={{
                    height: '100%',
                    display: 'flex',
                    flexDirection: 'column',
                    borderRadius: 2,
                    boxShadow: 2,
                    transition: 'transform 0.2s',
                    '&:hover': {
                      transform: 'translateY(-4px)',
                      boxShadow: 4
                    }
                  }}
                >
                  <Box
                    sx={{
                      p: 2,
                      bgcolor: alpha(getStatusColor(kpi.status), 0.05),
                      borderBottom: `1px solid ${alpha(getStatusColor(kpi.status), 0.1)}`,
                      display: 'flex',
                      alignItems: 'center',
                      gap: 1
                    }}
                  >
                    <InfoIcon sx={{ color: getStatusColor(kpi.status) }} />
                    <Typography variant="subtitle1" fontWeight="bold">
                      Status
                    </Typography>
                  </Box>
                  <CardContent sx={{ flexGrow: 1, display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center' }}>
                    <Box sx={{ textAlign: 'center' }}>
                      <Avatar
                        sx={{
                          width: 60,
                          height: 60,
                          bgcolor: alpha(getStatusColor(kpi.status), 0.1),
                          color: getStatusColor(kpi.status),
                          mx: 'auto',
                          mb: 1
                        }}
                      >
                        {kpi.status === 'active' ? '✓' :
                         kpi.status === 'draft' ? 'D' :
                         kpi.status === 'under_review' ? 'R' :
                         kpi.status === 'approved' ? 'A' :
                         kpi.status === 'archived' ? 'X' : '!'}
                      </Avatar>
                      <Typography
                        variant="body1"
                        sx={{
                          color: getStatusColor(kpi.status),
                          fontWeight: 'medium',
                          textTransform: 'capitalize'
                        }}
                      >
                        {kpi.status.replace('_', ' ')}
                      </Typography>

                      {kpi.status === 'approved' && kpi.approved_by_name && (
                        <Box sx={{ mt: 2, display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
                          <Chip
                            icon={<VerifiedUserIcon fontSize="small" />}
                            label={`Approved by ${kpi.approved_by_name}`}
                            size="small"
                            color="success"
                            sx={{ mb: 0.5 }}
                          />
                          {kpi.approved_at && (
                            <Typography variant="caption" color="text.secondary">
                              on {new Date(kpi.approved_at).toLocaleString()}
                            </Typography>
                          )}
                        </Box>
                      )}
                    </Box>
                  </CardContent>
                </Card>
              </Grid>

              <Grid item xs={12} md={6}>
                <Card
                  sx={{
                    height: '100%',
                    display: 'flex',
                    flexDirection: 'column',
                    borderRadius: 2,
                    boxShadow: 2,
                    transition: 'transform 0.2s',
                    '&:hover': {
                      transform: 'translateY(-4px)',
                      boxShadow: 4
                    }
                  }}
                >
                  <Box
                    sx={{
                      p: 2,
                      bgcolor: alpha(theme.palette.primary.main, 0.05),
                      borderBottom: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
                      display: 'flex',
                      alignItems: 'center',
                      gap: 1
                    }}
                  >
                    <CalendarIcon color="primary" />
                    <Typography variant="subtitle1" fontWeight="bold">
                      Academic Year
                    </Typography>
                  </Box>
                  <CardContent sx={{ flexGrow: 1, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                    {kpi.academic_year && (kpi.academic_year.name || kpi.academic_year.year) ? (
                      <Box sx={{ textAlign: 'center' }}>
                        <Avatar
                          sx={{
                            width: 60,
                            height: 60,
                            bgcolor: alpha(theme.palette.primary.main, 0.1),
                            color: theme.palette.primary.main,
                            mx: 'auto',
                            mb: 1,
                            fontWeight: 'bold',
                          }}
                        >
                          <CalendarIcon fontSize="large" />
                        </Avatar>
                        <Typography variant="body1" color="primary.main" fontWeight="medium">
                          {kpi.academic_year.name || kpi.academic_year.year}
                        </Typography>
                        {kpi.academic_year.is_current && (
                          <Chip
                            label="Current"
                            size="small"
                            color="success"
                            sx={{ mt: 1, fontWeight: 'bold' }}
                          />
                        )}
                      </Box>
                    ) : kpi.academic_year_name ? (
                      <Box sx={{ textAlign: 'center' }}>
                        <Avatar
                          sx={{
                            width: 60,
                            height: 60,
                            bgcolor: alpha(theme.palette.primary.main, 0.1),
                            color: theme.palette.primary.main,
                            mx: 'auto',
                            mb: 1,
                            fontWeight: 'bold',
                          }}
                        >
                          <CalendarIcon fontSize="large" />
                        </Avatar>
                        <Typography variant="body1" color="primary.main" fontWeight="medium">
                          {kpi.academic_year_name}
                        </Typography>
                      </Box>
                    ) : (
                      <Typography variant="body1" color="text.secondary">
                        Not specified
                      </Typography>
                    )}
                  </CardContent>
                </Card>
              </Grid>

              <Grid item xs={12} md={6}>
                <Card
                  sx={{
                    height: '100%',
                    display: 'flex',
                    flexDirection: 'column',
                    borderRadius: 2,
                    boxShadow: 2,
                    transition: 'transform 0.2s',
                    '&:hover': {
                      transform: 'translateY(-4px)',
                      boxShadow: 4
                    }
                  }}
                >
                  <Box
                    sx={{
                      p: 2,
                      bgcolor: alpha(theme.palette.info.main, 0.05),
                      borderBottom: `1px solid ${alpha(theme.palette.info.main, 0.1)}`,
                      display: 'flex',
                      alignItems: 'center',
                      gap: 1
                    }}
                  >
                    {getDirectionIcon(kpi.direction)}
                    <Typography variant="subtitle1" fontWeight="bold">
                      Direction
                    </Typography>
                  </Box>
                  <CardContent sx={{ flexGrow: 1, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                    <Box sx={{ textAlign: 'center' }}>
                      <Avatar
                        sx={{
                          width: 60,
                          height: 60,
                          bgcolor: alpha(theme.palette.info.main, 0.1),
                          color: theme.palette.info.main,
                          mx: 'auto',
                          mb: 1
                        }}
                      >
                        {getDirectionIcon(kpi.direction)}
                      </Avatar>
                      <Typography
                        variant="body1"
                        sx={{
                          color: theme.palette.info.main,
                          fontWeight: 'medium',
                          textTransform: 'capitalize'
                        }}
                      >
                        {kpi.direction.replace('_', ' ')}
                      </Typography>
                      <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                        {kpi.direction === 'increase' ? 'Higher values are better' :
                         kpi.direction === 'decrease' ? 'Lower values are better' :
                         kpi.direction === 'maintain' ? 'Exact target value is ideal' :
                         kpi.direction === 'range' ? 'Stay within min/max range' : ''}
                      </Typography>
                    </Box>
                  </CardContent>
                </Card>
              </Grid>

              <Grid item xs={12} md={6}>
                <Card
                  sx={{
                    height: '100%',
                    display: 'flex',
                    flexDirection: 'column',
                    borderRadius: 2,
                    boxShadow: 2,
                    transition: 'transform 0.2s',
                    '&:hover': {
                      transform: 'translateY(-4px)',
                      boxShadow: 4
                    }
                  }}
                >
                  <Box
                    sx={{
                      p: 2,
                      bgcolor: alpha(theme.palette.success.main, 0.05),
                      borderBottom: `1px solid ${alpha(theme.palette.success.main, 0.1)}`,
                      display: 'flex',
                      alignItems: 'center',
                      gap: 1
                    }}
                  >
                    <RepeatIcon color="success" />
                    <Typography variant="subtitle1" fontWeight="bold">
                      Frequency
                    </Typography>
                  </Box>
                  <CardContent sx={{ flexGrow: 1, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                    <Box sx={{ textAlign: 'center' }}>
                      <Avatar
                        sx={{
                          width: 60,
                          height: 60,
                          bgcolor: alpha(theme.palette.success.main, 0.1),
                          color: theme.palette.success.main,
                          mx: 'auto',
                          mb: 1
                        }}
                      >
                        <RepeatIcon fontSize="large" />
                      </Avatar>
                      <Typography
                        variant="body1"
                        sx={{
                          color: theme.palette.success.main,
                          fontWeight: 'medium',
                          textTransform: 'capitalize'
                        }}
                      >
                        {kpi.frequency}
                      </Typography>
                      <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                        {kpi.frequency === 'daily' ? 'Measured every day' :
                         kpi.frequency === 'weekly' ? 'Measured every week' :
                         kpi.frequency === 'monthly' ? 'Measured every month' :
                         kpi.frequency === 'quarterly' ? 'Measured every quarter' :
                         kpi.frequency === 'biannually' ? 'Measured twice a year' :
                         kpi.frequency === 'annually' ? 'Measured once a year' : ''}
                      </Typography>
                    </Box>
                  </CardContent>
                </Card>
              </Grid>

              <Grid item xs={12} md={6}>
                <Card
                  sx={{
                    height: '100%',
                    display: 'flex',
                    flexDirection: 'column',
                    borderRadius: 2,
                    boxShadow: 2,
                    transition: 'transform 0.2s',
                    '&:hover': {
                      transform: 'translateY(-4px)',
                      boxShadow: 4
                    }
                  }}
                >
                  <Box
                    sx={{
                      p: 2,
                      bgcolor: alpha(theme.palette.warning.main, 0.05),
                      borderBottom: `1px solid ${alpha(theme.palette.warning.main, 0.1)}`,
                      display: 'flex',
                      alignItems: 'center',
                      gap: 1
                    }}
                  >
                    <SourceIcon color="warning" />
                    <Typography variant="subtitle1" fontWeight="bold">
                      Data Source
                    </Typography>
                  </Box>
                  <CardContent sx={{ flexGrow: 1, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                    <Box sx={{ textAlign: 'center' }}>
                      <Avatar
                        sx={{
                          width: 60,
                          height: 60,
                          bgcolor: alpha(theme.palette.warning.main, 0.1),
                          color: theme.palette.warning.main,
                          mx: 'auto',
                          mb: 1
                        }}
                      >
                        <SourceIcon fontSize="large" />
                      </Avatar>
                      <Typography
                        variant="body1"
                        sx={{
                          color: theme.palette.warning.main,
                          fontWeight: 'medium',
                          textTransform: 'capitalize'
                        }}
                      >
                        {kpi.data_source ? kpi.data_source.replace('_', ' ') : 'Not specified'}
                      </Typography>
                      {kpi.data_source_details && (
                        <Tooltip title={kpi.data_source_details}>
                          <Typography variant="body2" color="text.secondary" sx={{ mt: 1, cursor: 'help' }}>
                            {kpi.data_source_details.length > 50
                              ? `${kpi.data_source_details.substring(0, 50)}...`
                              : kpi.data_source_details}
                          </Typography>
                        </Tooltip>
                      )}
                    </Box>
                  </CardContent>
                </Card>
              </Grid>

              <Grid item xs={12}>
                <Divider sx={{ my: 2 }} />
              </Grid>

              <Grid item xs={12} sm={6}>
                <Typography variant="body2" color="text.secondary">
                  <strong>Created:</strong> {formatDate(kpi.created_at)}
                </Typography>
              </Grid>

              <Grid item xs={12} sm={6}>
                <Typography variant="body2" color="text.secondary">
                  <strong>Last Updated:</strong> {formatDate(kpi.updated_at)}
                </Typography>
              </Grid>
            </Grid>
            </Box>
          </Paper>

          <Paper
            sx={{
              p: 0,
              mb: 3,
              overflow: 'hidden',
              borderRadius: 2,
              boxShadow: `0 4px 20px ${alpha(theme.palette.primary.main, 0.15)}`,
              transition: 'transform 0.3s, box-shadow 0.3s',
              '&:hover': {
                boxShadow: `0 8px 30px ${alpha(theme.palette.primary.main, 0.2)}`,
                transform: 'translateY(-5px)'
              }
            }}
          >
            <Box
              sx={{
                background: `linear-gradient(to right, ${alpha(theme.palette.primary.main, 0.1)}, ${alpha(theme.palette.primary.light, 0.05)})`,
                p: 2,
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                borderBottom: `1px solid ${alpha(theme.palette.primary.main, 0.2)}`
              }}
            >
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1.5 }}>
                <Avatar
                  sx={{
                    bgcolor: alpha(theme.palette.primary.main, 0.2),
                    color: theme.palette.primary.main,
                    boxShadow: `0 2px 8px ${alpha(theme.palette.primary.main, 0.2)}`,
                  }}
                >
                  <SchoolIcon />
                </Avatar>
                <Typography
                  variant="h6"
                  sx={{
                    fontWeight: 'bold',
                    color: theme.palette.primary.main,
                    textShadow: `0 1px 2px ${alpha(theme.palette.primary.main, 0.1)}`,
                  }}
                >
                  University Assignments
                </Typography>
              </Box>
              <Button
                component={RouterLink}
                to={`/assignments/create?kpi=${kpi.id}`}
                variant="contained"
                color="primary"
                startIcon={<AddIcon />}
                size="small"
                sx={{
                  px: 2,
                  py: 1,
                  borderRadius: 2,
                  background: `linear-gradient(45deg, ${theme.palette.primary.main} 30%, ${theme.palette.primary.light} 90%)`,
                  boxShadow: `0 2px 8px ${alpha(theme.palette.primary.main, 0.3)}`,
                  transition: 'all 0.3s',
                  '&:hover': {
                    boxShadow: `0 4px 12px ${alpha(theme.palette.primary.main, 0.4)}`,
                    transform: 'translateY(-2px)'
                  }
                }}
              >
                Assign to University
              </Button>
            </Box>

            {assignments && assignments.length > 0 ? (
              <TableContainer sx={{ maxHeight: 400 }}>
                <Table stickyHeader size="small">
                  <TableHead>
                    <TableRow>
                      <TableCell
                        sx={{
                          fontWeight: 'bold',
                          bgcolor: alpha(theme.palette.primary.main, 0.05),
                          borderBottom: `2px solid ${alpha(theme.palette.primary.main, 0.2)}`,
                          py: 1.5
                        }}
                      >
                        University
                      </TableCell>
                      <TableCell
                        sx={{
                          fontWeight: 'bold',
                          bgcolor: alpha(theme.palette.primary.main, 0.05),
                          borderBottom: `2px solid ${alpha(theme.palette.primary.main, 0.2)}`,
                          py: 1.5
                        }}
                      >
                        Academic Year
                      </TableCell>
                      <TableCell
                        sx={{
                          fontWeight: 'bold',
                          bgcolor: alpha(theme.palette.primary.main, 0.05),
                          borderBottom: `2px solid ${alpha(theme.palette.primary.main, 0.2)}`,
                          py: 1.5
                        }}
                      >
                        Target
                      </TableCell>
                      <TableCell
                        sx={{
                          fontWeight: 'bold',
                          bgcolor: alpha(theme.palette.primary.main, 0.05),
                          borderBottom: `2px solid ${alpha(theme.palette.primary.main, 0.2)}`,
                          py: 1.5
                        }}
                      >
                        Status
                      </TableCell>
                      <TableCell
                        sx={{
                          fontWeight: 'bold',
                          bgcolor: alpha(theme.palette.primary.main, 0.05),
                          borderBottom: `2px solid ${alpha(theme.palette.primary.main, 0.2)}`,
                          py: 1.5
                        }}
                      >
                        Actions
                      </TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {assignments.map((assignment) => (
                      <TableRow
                        key={assignment.id}
                        hover
                        sx={{
                          '&:nth-of-type(odd)': { bgcolor: alpha(theme.palette.primary.main, 0.02) },
                          '&:hover': {
                            bgcolor: alpha(theme.palette.primary.main, 0.05),
                            boxShadow: `inset 0 0 0 1px ${alpha(theme.palette.primary.main, 0.1)}`
                          },
                          transition: 'all 0.2s'
                        }}
                      >
                        <TableCell>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <Avatar
                              sx={{
                                width: 32,
                                height: 32,
                                bgcolor: alpha(theme.palette.primary.main, 0.1),
                                color: theme.palette.primary.main,
                                boxShadow: `0 2px 4px ${alpha(theme.palette.primary.main, 0.1)}`,
                              }}
                            >
                              <SchoolIcon fontSize="small" />
                            </Avatar>
                            <Link
                              component={RouterLink}
                              to={`/universities/${assignment.university?.id}`}
                              underline="hover"
                              color="primary"
                              sx={{
                                fontWeight: 'medium',
                                transition: 'all 0.2s',
                                '&:hover': {
                                  color: theme.palette.primary.dark,
                                  textDecoration: 'underline'
                                }
                              }}
                            >
                              {assignment.university?.name || assignment.university_name || 'Unknown University'}
                            </Link>
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Chip
                            icon={<CalendarIcon fontSize="small" />}
                            label={assignment.academic_year?.name || assignment.academic_year_name || 'N/A'}
                            size="small"
                            sx={{
                              bgcolor: alpha(theme.palette.primary.main, 0.1),
                              color: theme.palette.primary.main,
                              border: `1px solid ${alpha(theme.palette.primary.main, 0.3)}`,
                              fontWeight: 'medium',
                              transition: 'all 0.2s',
                              '&:hover': {
                                bgcolor: alpha(theme.palette.primary.main, 0.2),
                                transform: 'translateY(-1px)'
                              }
                            }}
                          />
                        </TableCell>
                        <TableCell>
                          <Chip
                            label={`${assignment.target || assignment.target_value || 'N/A'} ${kpi.measurement_unit?.symbol || kpi.measurement_unit_symbol || '-'}`}
                            size="small"
                            sx={{
                              bgcolor: alpha(theme.palette.info.main, 0.1),
                              color: theme.palette.info.main,
                              border: `1px solid ${alpha(theme.palette.info.main, 0.3)}`,
                              fontWeight: 'medium',
                              transition: 'all 0.2s',
                              '&:hover': {
                                bgcolor: alpha(theme.palette.info.main, 0.2),
                                transform: 'translateY(-1px)'
                              }
                            }}
                          />
                        </TableCell>
                        <TableCell>
                          <Chip
                            label={(assignment.status || 'unknown').replace('_', ' ')}
                            size="small"
                            sx={{
                              bgcolor: alpha(
                                assignment.status === 'active' ? theme.palette.success.main :
                                assignment.status === 'pending' ? theme.palette.warning.main :
                                theme.palette.grey[500],
                                0.1
                              ),
                              color: assignment.status === 'active' ? theme.palette.success.main :
                                     assignment.status === 'pending' ? theme.palette.warning.main :
                                     theme.palette.grey[700],
                              border: `1px solid ${alpha(
                                assignment.status === 'active' ? theme.palette.success.main :
                                assignment.status === 'pending' ? theme.palette.warning.main :
                                theme.palette.grey[500],
                                0.3
                              )}`,
                              textTransform: 'capitalize',
                              fontWeight: 'medium',
                              transition: 'all 0.2s',
                              '&:hover': {
                                bgcolor: alpha(
                                  assignment.status === 'active' ? theme.palette.success.main :
                                  assignment.status === 'pending' ? theme.palette.warning.main :
                                  theme.palette.grey[500],
                                  0.2
                                ),
                                transform: 'translateY(-1px)'
                              }
                            }}
                          />
                        </TableCell>
                        <TableCell>
                          <Button
                            component={RouterLink}
                            to={`/assignments/${assignment.id}`}
                            size="small"
                            variant="contained"
                            color="primary"
                            sx={{
                              borderRadius: 6,
                              px: 2,
                              py: 0.5,
                              background: `linear-gradient(45deg, ${theme.palette.primary.main} 30%, ${theme.palette.primary.light} 90%)`,
                              boxShadow: `0 2px 5px ${alpha(theme.palette.primary.main, 0.3)}`,
                              transition: 'all 0.3s',
                              '&:hover': {
                                boxShadow: `0 4px 10px ${alpha(theme.palette.primary.main, 0.4)}`,
                                transform: 'translateY(-2px)'
                              }
                            }}
                          >
                            View
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            ) : (
              <Box sx={{ p: 4, textAlign: 'center' }}>
                <Avatar
                  sx={{
                    width: 80,
                    height: 80,
                    bgcolor: alpha(theme.palette.primary.main, 0.1),
                    color: theme.palette.primary.main,
                    mx: 'auto',
                    mb: 2,
                    boxShadow: `0 4px 12px ${alpha(theme.palette.primary.main, 0.2)}`,
                    transition: 'transform 0.3s',
                    '&:hover': {
                      transform: 'scale(1.05) rotate(5deg)'
                    }
                  }}
                >
                  <SchoolIcon sx={{ fontSize: 40 }} />
                </Avatar>
                <Typography variant="h6" color="text.secondary" gutterBottom>
                  No university assignments
                </Typography>
                <Typography variant="body2" color="text.secondary" paragraph sx={{ maxWidth: 400, mx: 'auto' }}>
                  This KPI hasn't been assigned to any universities yet. Assign it to start tracking performance.
                </Typography>
                <Button
                  component={RouterLink}
                  to={`/assignments/create?kpi=${kpi.id}`}
                  variant="contained"
                  color="primary"
                  startIcon={<AddIcon />}
                  sx={{
                    mt: 2,
                    px: 3,
                    py: 1,
                    borderRadius: 2,
                    background: `linear-gradient(45deg, ${theme.palette.primary.main} 30%, ${theme.palette.primary.light} 90%)`,
                    boxShadow: `0 4px 10px ${alpha(theme.palette.primary.main, 0.3)}`,
                    transition: 'all 0.3s',
                    '&:hover': {
                      boxShadow: `0 6px 15px ${alpha(theme.palette.primary.main, 0.4)}`,
                      transform: 'translateY(-3px)'
                    }
                  }}
                >
                  Assign to University
                </Button>
              </Box>
            )}
          </Paper>
        </Grid>

        <Grid item xs={12} md={4}>
          <Paper
            sx={{
              p: 0,
              mb: 3,
              overflow: 'hidden',
              borderRadius: 2,
              boxShadow: 3,
              transition: 'transform 0.3s, box-shadow 0.3s',
              '&:hover': {
                boxShadow: 6,
                transform: 'translateY(-4px)'
              }
            }}
          >
            <Box
              sx={{
                bgcolor: alpha(theme.palette.success.main, 0.1),
                p: 2,
                display: 'flex',
                alignItems: 'center',
                gap: 1.5,
                borderBottom: `1px solid ${alpha(theme.palette.success.main, 0.2)}`
              }}
            >
              <Avatar
                sx={{
                  bgcolor: alpha(theme.palette.success.main, 0.2),
                  color: theme.palette.success.main,
                }}
              >
                <BarChartIcon />
              </Avatar>
              <Typography
                variant="h6"
                sx={{
                  fontWeight: 'bold',
                  color: theme.palette.success.main
                }}
              >
                Statistics
              </Typography>
            </Box>

            <Box sx={{ p: 2 }}>
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <Card
                    sx={{
                      p: 2,
                      display: 'flex',
                      alignItems: 'center',
                      gap: 2,
                      bgcolor: alpha(theme.palette.primary.main, 0.05),
                      borderRadius: 2,
                      boxShadow: 1,
                      transition: 'all 0.3s',
                      '&:hover': {
                        transform: 'translateY(-2px)',
                        boxShadow: 3,
                        bgcolor: alpha(theme.palette.primary.main, 0.08)
                      }
                    }}
                  >
                    <Avatar
                      sx={{
                        bgcolor: alpha(theme.palette.primary.main, 0.1),
                        color: theme.palette.primary.main,
                        width: 48,
                        height: 48,
                        boxShadow: `0 2px 8px ${alpha(theme.palette.primary.main, 0.2)}`
                      }}
                    >
                      <SchoolIcon />
                    </Avatar>
                    <Box>
                      <Typography variant="body2" color="text.secondary" fontWeight="medium">
                        Universities Assigned
                      </Typography>
                      <Typography variant="h6" color="primary.main" fontWeight="bold">
                        {statistics.universityCount || 0}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {statistics.universityCount === 1 ? 'University' : 'Universities'} using this KPI
                      </Typography>
                    </Box>
                  </Card>
                </Grid>

                <Grid item xs={12}>
                  <Card
                    sx={{
                      p: 2,
                      display: 'flex',
                      alignItems: 'center',
                      gap: 2,
                      bgcolor: alpha(theme.palette.success.main, 0.05),
                      borderRadius: 2,
                      boxShadow: 1,
                      transition: 'all 0.3s',
                      '&:hover': {
                        transform: 'translateY(-2px)',
                        boxShadow: 3,
                        bgcolor: alpha(theme.palette.success.main, 0.08)
                      }
                    }}
                  >
                    <Avatar
                      sx={{
                        bgcolor: alpha(theme.palette.success.main, 0.1),
                        color: theme.palette.success.main,
                        width: 48,
                        height: 48,
                        boxShadow: `0 2px 8px ${alpha(theme.palette.success.main, 0.2)}`
                      }}
                    >
                      <AssignmentIcon />
                    </Avatar>
                    <Box>
                      <Typography variant="body2" color="text.secondary" fontWeight="medium">
                        Active Assignments
                      </Typography>
                      <Typography variant="h6" color="success.main" fontWeight="bold">
                        {statistics.activeAssignments || 0}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {statistics.activeAssignments === 1 ? 'Assignment' : 'Assignments'} currently active
                      </Typography>
                    </Box>
                  </Card>
                </Grid>

                <Grid item xs={12}>
                  <Card
                    sx={{
                      p: 2,
                      display: 'flex',
                      alignItems: 'center',
                      gap: 2,
                      bgcolor: alpha(theme.palette.info.main, 0.05),
                      borderRadius: 2,
                      boxShadow: 1,
                      transition: 'all 0.3s',
                      '&:hover': {
                        transform: 'translateY(-2px)',
                        boxShadow: 3,
                        bgcolor: alpha(theme.palette.info.main, 0.08)
                      }
                    }}
                  >
                    <Avatar
                      sx={{
                        bgcolor: alpha(theme.palette.info.main, 0.1),
                        color: theme.palette.info.main,
                        width: 48,
                        height: 48,
                        boxShadow: `0 2px 8px ${alpha(theme.palette.info.main, 0.2)}`
                      }}
                    >
                      <StraightenIcon />
                    </Avatar>
                    <Box>
                      <Typography variant="body2" color="text.secondary" fontWeight="medium">
                        Average Target
                      </Typography>
                      <Typography variant="h6" color="info.main" fontWeight="bold">
                        {statistics.averageTarget || 'N/A'}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {statistics.averageTarget !== 'N/A' ? 'Average target across all assignments' : 'No target data available'}
                      </Typography>
                    </Box>
                  </Card>
                </Grid>

                <Grid item xs={12}>
                  <Card
                    sx={{
                      p: 2,
                      display: 'flex',
                      alignItems: 'center',
                      gap: 2,
                      bgcolor: alpha(theme.palette.warning.main, 0.05),
                      borderRadius: 2,
                      boxShadow: 1,
                      transition: 'all 0.3s',
                      '&:hover': {
                        transform: 'translateY(-2px)',
                        boxShadow: 3,
                        bgcolor: alpha(theme.palette.warning.main, 0.08)
                      }
                    }}
                  >
                    <Avatar
                      sx={{
                        bgcolor: alpha(theme.palette.warning.main, 0.1),
                        color: theme.palette.warning.main,
                        width: 48,
                        height: 48,
                        boxShadow: `0 2px 8px ${alpha(theme.palette.warning.main, 0.2)}`
                      }}
                    >
                      <TimelineIcon />
                    </Avatar>
                    <Box sx={{ flexGrow: 1 }}>
                      <Typography variant="body2" color="text.secondary" fontWeight="medium">
                        Completion Rate
                      </Typography>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <Box sx={{ flexGrow: 1, mr: 2 }}>
                          <LinearProgress
                            variant="determinate"
                            value={typeof statistics.completionRate === 'number' ? Math.min(100, Math.max(0, statistics.completionRate)) : 0}
                            sx={{
                              height: 8,
                              borderRadius: 4,
                              bgcolor: alpha(theme.palette.warning.main, 0.1),
                              '& .MuiLinearProgress-bar': {
                                bgcolor: theme.palette.warning.main
                              }
                            }}
                          />
                        </Box>
                        <Typography variant="h6" color="warning.main" fontWeight="bold">
                          {typeof statistics.completionRate === 'number' ?
                            `${Math.min(100, Math.max(0, statistics.completionRate)).toFixed(1)}%` :
                            '0.0%'}
                        </Typography>
                      </Box>
                      <Typography variant="caption" color="text.secondary">
                        Overall completion across universities
                      </Typography>
                    </Box>
                  </Card>
                </Grid>
              </Grid>
            </Box>
          </Paper>

          <Paper
            sx={{
              p: 0,
              overflow: 'hidden',
              borderRadius: 2,
              boxShadow: 3,
              transition: 'transform 0.3s, box-shadow 0.3s',
              mb: 3,
              '&:hover': {
                boxShadow: 6,
                transform: 'translateY(-4px)'
              }
            }}
          >
            <Box
              sx={{
                bgcolor: alpha(theme.palette.warning.main, 0.1),
                p: 2,
                display: 'flex',
                alignItems: 'center',
                gap: 1.5,
                borderBottom: `1px solid ${alpha(theme.palette.warning.main, 0.2)}`
              }}
            >
              <Avatar
                sx={{
                  bgcolor: alpha(theme.palette.warning.main, 0.2),
                  color: theme.palette.warning.main,
                }}
              >
                <BusinessIcon />
              </Avatar>
              <Typography
                variant="h6"
                sx={{
                  fontWeight: 'bold',
                  color: theme.palette.warning.main
                }}
              >
                Responsibility
              </Typography>
            </Box>

            <Box sx={{ p: 3 }}>
              <List disablePadding>
                {kpi.responsible_department && (
                  <ListItem sx={{ px: 0, py: 1 }}>
                    <ListItemIcon sx={{ minWidth: 40 }}>
                      <BusinessIcon color="warning" fontSize="small" />
                    </ListItemIcon>
                    <ListItemText
                      primary="Department"
                      secondary={kpi.responsible_department}
                      primaryTypographyProps={{ variant: 'body2', color: 'text.secondary' }}
                      secondaryTypographyProps={{ variant: 'body1', fontWeight: 'medium' }}
                    />
                  </ListItem>
                )}

                {kpi.responsible_person && (
                  <ListItem sx={{ px: 0, py: 1 }}>
                    <ListItemIcon sx={{ minWidth: 40 }}>
                      <PersonIcon color="warning" fontSize="small" />
                    </ListItemIcon>
                    <ListItemText
                      primary="Responsible Person"
                      secondary={kpi.responsible_person}
                      primaryTypographyProps={{ variant: 'body2', color: 'text.secondary' }}
                      secondaryTypographyProps={{ variant: 'body1', fontWeight: 'medium' }}
                    />
                  </ListItem>
                )}

                {kpi.contact_email && (
                  <ListItem sx={{ px: 0, py: 1 }}>
                    <ListItemIcon sx={{ minWidth: 40 }}>
                      <EmailIcon color="warning" fontSize="small" />
                    </ListItemIcon>
                    <ListItemText
                      primary="Contact Email"
                      secondary={kpi.contact_email}
                      primaryTypographyProps={{ variant: 'body2', color: 'text.secondary' }}
                      secondaryTypographyProps={{ variant: 'body1', fontWeight: 'medium' }}
                    />
                  </ListItem>
                )}

                {!kpi.responsible_department && !kpi.responsible_person && !kpi.contact_email && (
                  <Typography variant="body1" color="text.secondary" sx={{ py: 2, textAlign: 'center' }}>
                    No responsibility information provided.
                  </Typography>
                )}
              </List>
            </Box>
          </Paper>
        </Grid>
      </Grid>

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteDialogOpen}
        onClose={handleDeleteCancel}
        aria-labelledby="delete-dialog-title"
        aria-describedby="delete-dialog-description"
      >
        <DialogTitle id="delete-dialog-title" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <WarningIcon color="error" />
          Confirm Delete
        </DialogTitle>
        <DialogContent>
          <DialogContentText id="delete-dialog-description">
            Are you sure you want to delete the KPI <strong>{kpi?.title}</strong>?
            <Box sx={{ mt: 2, p: 2, bgcolor: alpha(theme.palette.error.main, 0.1), borderRadius: 1 }}>
              <Typography variant="body2" color="error" fontWeight="medium">
                Warning: This action cannot be undone. Deleting this KPI will also remove all associated assignments and reports.
              </Typography>
            </Box>
          </DialogContentText>
        </DialogContent>
        <DialogActions sx={{ px: 3, pb: 2 }}>
          <Button
            onClick={handleDeleteCancel}
            variant="outlined"
            disabled={deleteInProgress}
          >
            Cancel
          </Button>
          <Button
            onClick={handleDeleteConfirm}
            color="error"
            variant="contained"
            disabled={deleteInProgress}
            startIcon={deleteInProgress ? <CircularProgress size={20} color="inherit" /> : <DeleteIcon />}
            sx={{ px: 2 }}
          >
            {deleteInProgress ? 'Deleting...' : 'Delete'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={6000}
        onClose={handleSnackbarClose}
        message={snackbarMessage}
      />
    </Container>
  )
}

export default KPIDetail
