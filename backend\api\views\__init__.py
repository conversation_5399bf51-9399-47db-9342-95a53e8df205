# Import from module files
from .kpi_definitions import (
    TagViewSet, AcademicYearViewSet, ThemeViewSet,
    SubThemeViewSet, MeasurementUnitViewSet, KPIViewSet,
    ReportTemplateViewSet
)

from .kpi_assignments import (
    UniversityKPIAssignmentViewSet, OfficeKPIAssignmentViewSet, UserKPIAssignmentViewSet,
    KPIReportViewSet, OfficeKPIReportViewSet, UserKPIReportViewSet
)

# Import from parent views.py file
import sys
import os
import importlib.util

# Get the parent directory path
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
views_path = os.path.join(parent_dir, 'views.py')

# Check if views.py exists
if os.path.exists(views_path):
    # Import directly from the file
    spec = importlib.util.spec_from_file_location('api.views_module', views_path)
    views_module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(views_module)

    # Import the missing viewsets and functions
    UserViewSet = views_module.UserViewSet
    UniversityViewSet = views_module.UniversityViewSet
    DomainViewSet = views_module.DomainViewSet
    UniversityClassificationViewSet = views_module.UniversityClassificationViewSet
    OfficeViewSet = views_module.OfficeViewSet
    UserPositionViewSet = views_module.UserPositionViewSet
    AuditLogViewSet = views_module.AuditLogViewSet
    current_user = views_module.current_user
    moe_dashboard = views_module.moe_dashboard
    university_dashboard = views_module.university_dashboard
    current_tenant = views_module.current_tenant
    api_documentation = views_module.api_documentation
    kpi_performance = views_module.kpi_performance

# Define __all__ to explicitly export these symbols
__all__ = [
    'TagViewSet', 'AcademicYearViewSet', 'ThemeViewSet', 'SubThemeViewSet',
    'MeasurementUnitViewSet', 'KPIViewSet', 'UniversityKPIAssignmentViewSet',
    'OfficeKPIAssignmentViewSet', 'UserKPIAssignmentViewSet', 'KPIReportViewSet',
    'OfficeKPIReportViewSet', 'UserKPIReportViewSet', 'UserViewSet',
    'UniversityViewSet', 'DomainViewSet', 'UniversityClassificationViewSet',
    'OfficeViewSet', 'UserPositionViewSet', 'AuditLogViewSet', 'ReportTemplateViewSet',
    'current_user', 'moe_dashboard', 'university_dashboard', 'current_tenant', 'api_documentation',
    'kpi_performance'
]
