from rest_framework import serializers
from kpi_definitions.models import (
    Tag, AcademicYear, Theme, SubTheme, MeasurementUnit, KPI, ReportTemplate
)


class TagSerializer(serializers.ModelSerializer):
    kpi_count = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = Tag
        fields = ['id', 'name', 'description', 'color', 'created_at', 'updated_at', 'kpi_count']
        read_only_fields = ['created_at', 'updated_at']

    def get_kpi_count(self, obj):
        return obj.kpis.count()


class AcademicYearSerializer(serializers.ModelSerializer):
    kpi_count = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = AcademicYear
        fields = ['id', 'name', 'start_date', 'end_date', 'description', 'is_current', 'kpi_count']

    def get_kpi_count(self, obj):
        return obj.kpis.count()


class ThemeSerializer(serializers.ModelSerializer):
    created_by_username = serializers.StringRelatedField(source='created_by.username', read_only=True)
    subtheme_count = serializers.SerializerMethodField(read_only=True)
    kpi_count = serializers.SerializerMethodField(read_only=True)
    university_count = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = Theme
        fields = [
            'id', 'name', 'description', 'slug', 'icon', 'color', 'order', 'is_active',
            'created_by', 'created_by_username', 'created_at', 'updated_at',
            'subtheme_count', 'kpi_count', 'university_count'
        ]
        read_only_fields = ['slug', 'created_at', 'updated_at', 'created_by_username']  # Slug is auto-generated from name

    def get_subtheme_count(self, obj):
        return obj.subthemes.count()

    def get_kpi_count(self, obj):
        return obj.kpis.count()

    def get_university_count(self, obj):
        # This is a placeholder - in a real implementation, you would count universities using this theme
        # For now, we'll return 0 or a random number
        return 0

    def create(self, validated_data):
        # Set the created_by field to the current user
        request = self.context.get('request')
        if request and hasattr(request, 'user'):
            validated_data['created_by'] = request.user
        return super().create(validated_data)


class SubThemeSerializer(serializers.ModelSerializer):
    theme_name = serializers.StringRelatedField(source='theme.name', read_only=True)
    theme_id = serializers.PrimaryKeyRelatedField(
        queryset=Theme.objects.all(),
        source='theme',
        required=False
    )
    created_by_username = serializers.StringRelatedField(source='created_by.username', read_only=True)
    kpi_count = serializers.SerializerMethodField(read_only=True)
    university_count = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = SubTheme
        fields = [
            'id', 'theme', 'theme_id', 'theme_name', 'name', 'description', 'slug',
            'icon', 'color', 'order', 'is_active', 'created_at', 'updated_at', 'created_by_username',
            'kpi_count', 'university_count'
        ]
        read_only_fields = ['slug', 'created_at', 'updated_at', 'created_by_username']  # Slug is auto-generated from name

    def get_kpi_count(self, obj):
        return obj.kpis.count()

    def get_university_count(self, obj):
        # This is a placeholder - in a real implementation, you would count universities using this subtheme
        # For now, we'll return 0 or a random number
        return 0

    def create(self, validated_data):
        # Log the validated data for debugging
        import logging
        logger = logging.getLogger(__name__)
        logger.debug(f"Creating SubTheme with validated data: {validated_data}")

        # Ensure theme is set
        if 'theme' not in validated_data:
            raise serializers.ValidationError({'theme': 'Theme is required'})

        # Set the created_by field to the current user
        request = self.context.get('request')
        if request and hasattr(request, 'user'):
            validated_data['created_by'] = request.user

        return super().create(validated_data)

    def update(self, instance, validated_data):
        # Log the validated data for debugging
        import logging
        logger = logging.getLogger(__name__)
        logger.debug(f"Updating SubTheme with validated data: {validated_data}")

        return super().update(instance, validated_data)

    def validate(self, data):
        # For update operations, we don't need to require theme if it's not changing
        if self.instance is None:  # This is a create operation
            if 'theme' not in data:
                raise serializers.ValidationError({'theme': 'Theme is required for new subthemes'})
        return data


class MeasurementUnitSerializer(serializers.ModelSerializer):
    kpi_count = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = MeasurementUnit
        fields = ['id', 'name', 'symbol', 'description', 'is_percentage', 'decimal_places', 'kpi_count']

    def get_kpi_count(self, obj):
        return obj.kpis.count()  # Use kpis to access the reverse relation (related_name in KPI model)


class KPISerializer(serializers.ModelSerializer):
    theme_name = serializers.StringRelatedField(source='theme.name', read_only=True)
    sub_theme_name = serializers.StringRelatedField(source='sub_theme.name', read_only=True)
    measurement_unit_name = serializers.StringRelatedField(source='measurement_unit.name', read_only=True)
    measurement_unit_symbol = serializers.StringRelatedField(source='measurement_unit.symbol', read_only=True)
    academic_year_name = serializers.StringRelatedField(source='academic_year.name', read_only=True)

    # Tags
    tags = TagSerializer(many=True, read_only=True)
    tag_ids = serializers.PrimaryKeyRelatedField(
        many=True,
        queryset=Tag.objects.all(),
        source='tags',
        write_only=True,
        required=False
    )

    # Audit fields
    created_by_name = serializers.SerializerMethodField(read_only=True)
    approved_by_name = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = KPI
        fields = [
            'id', 'title', 'description', 'calculation_method',
            'theme', 'theme_name', 'sub_theme', 'sub_theme_name',
            'measurement_unit', 'measurement_unit_name', 'measurement_unit_symbol',
            'frequency', 'direction', 'data_source', 'data_source_details',
            'academic_year', 'academic_year_name',
            'responsible_department', 'responsible_person', 'contact_email',
            'status', 'is_active', 'is_public', 'notes',
            'tags', 'tag_ids',
            'created_at', 'updated_at', 'created_by', 'created_by_name',
            'approved_by', 'approved_by_name', 'approved_at'
        ]
        read_only_fields = ['created_at', 'updated_at', 'created_by', 'approved_by', 'approved_at']

    def get_created_by_name(self, obj):
        if obj.created_by:
            return f"{obj.created_by.first_name} {obj.created_by.last_name}".strip() or obj.created_by.username
        return None

    def get_approved_by_name(self, obj):
        if obj.approved_by:
            return f"{obj.approved_by.first_name} {obj.approved_by.last_name}".strip() or obj.approved_by.username
        return None

    def create(self, validated_data):
        # Set the created_by field to the current user
        request = self.context.get('request')
        if request and hasattr(request, 'user'):
            validated_data['created_by'] = request.user
        return super().create(validated_data)


class ReportTemplateSerializer(serializers.ModelSerializer):
    created_by_username = serializers.StringRelatedField(source='created_by.username', read_only=True)
    duplicate = serializers.BooleanField(write_only=True, required=False, default=False)
    new_title = serializers.CharField(write_only=True, required=False, allow_blank=True)

    class Meta:
        model = ReportTemplate
        fields = [
            'id', 'title', 'description', 'content', 'created_by', 'created_by_username',
            'created_at', 'updated_at', 'is_active', 'duplicate', 'new_title'
        ]
        read_only_fields = ['created_at', 'updated_at', 'created_by_username']

    def create(self, validated_data):
        # Extract non-model fields
        duplicate = validated_data.pop('duplicate', False)
        new_title = validated_data.pop('new_title', None)

        # If this is a duplicate request
        if duplicate and self.context.get('template_id'):
            # Get the template to duplicate
            try:
                template_to_duplicate = ReportTemplate.objects.get(id=self.context.get('template_id'))
                # Create a duplicate with the new title if provided
                return template_to_duplicate.duplicate(new_title)
            except ReportTemplate.DoesNotExist:
                raise serializers.ValidationError({'template_id': 'Template not found'})

        # Normal creation
        return super().create(validated_data)
