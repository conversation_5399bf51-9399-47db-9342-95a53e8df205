#!/usr/bin/env python3
"""
Simple authentication test to verify login works.
"""

import os
import django
import sys

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'moe_kpi_tracker.settings')
django.setup()

from django.contrib.auth.models import User
from django_tenants.utils import schema_context, tenant_context
from universities.models import University
from django.contrib.auth import authenticate
from django.db import connection

def test_authentication():
    """Test authentication directly using Django's authenticate function."""
    print("=== Testing Django Authentication ===")
    
    # Test public schema authentication
    with schema_context('public'):
        print(f"\nTesting in public schema (current: {connection.schema_name})")
        
        # List users
        users = User.objects.all()
        print(f"Users in public schema: {[u.username for u in users]}")
        
        # Test authentication with email
        user = authenticate(username='<EMAIL>', password='admin123')
        if user:
            print(f"✅ Authentication successful with email: {user.username}")
        else:
            print("❌ Authentication failed with email")
            
        # Test authentication with username
        user = authenticate(username='admin', password='admin123')
        if user:
            print(f"✅ Authentication successful with username: {user.username}")
        else:
            print("❌ Authentication failed with username")
    
    # Test university schema authentication
    universities = University.objects.exclude(schema_name='public')
    if universities.exists():
        university = universities.first()
        with tenant_context(university):
            print(f"\nTesting in {university.schema_name} schema (current: {connection.schema_name})")
            
            # List users
            users = User.objects.all()
            print(f"Users in {university.schema_name} schema: {[u.username for u in users[:5]]}")  # Show first 5
            
            # Test authentication with email
            user = authenticate(username=f'admin@{university.schema_name}.edu.et', password='admin123')
            if user:
                print(f"✅ Authentication successful with email: {user.username}")
            else:
                print("❌ Authentication failed with email")
                
            # Test authentication with username
            user = authenticate(username='admin', password='admin123')
            if user:
                print(f"✅ Authentication successful with username: {user.username}")
            else:
                print("❌ Authentication failed with username")

def test_manual_user_lookup():
    """Test manual user lookup to see what's happening."""
    print("\n=== Testing Manual User Lookup ===")
    
    # Test public schema
    with schema_context('public'):
        print(f"\nPublic schema users:")
        users = User.objects.all()
        for user in users:
            print(f"  Username: {user.username}, Email: {user.email}, Active: {user.is_active}")
            
            # Test password check
            if user.check_password('admin123'):
                print(f"    ✅ Password check successful for {user.username}")
            else:
                print(f"    ❌ Password check failed for {user.username}")
    
    # Test university schema
    universities = University.objects.exclude(schema_name='public')
    if universities.exists():
        university = universities.first()
        with tenant_context(university):
            print(f"\n{university.schema_name} schema users (first 3):")
            users = User.objects.all()[:3]
            for user in users:
                print(f"  Username: {user.username}, Email: {user.email}, Active: {user.is_active}")
                
                # Test password check
                if user.check_password('admin123'):
                    print(f"    ✅ Password check successful for {user.username}")
                else:
                    print(f"    ❌ Password check failed for {user.username}")

if __name__ == "__main__":
    try:
        test_authentication()
        test_manual_user_lookup()
        print("\n✅ Authentication test complete!")
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
