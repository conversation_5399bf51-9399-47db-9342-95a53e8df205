from rest_framework import viewsets, permissions, status
from rest_framework.response import Response
from rest_framework.decorators import action
from django.db import connection
from django_tenants.utils import get_public_schema_name

from kpi_definitions.models import (
    Tag, AcademicYear, Theme, SubTheme, MeasurementUnit, KPI, ReportTemplate
)
from api.serializers.kpi_definitions import (
    TagSerializer, AcademicYearSerializer, ThemeSerializer,
    SubThemeSerializer, MeasurementUnitSerializer, KPISerializer,
    ReportTemplateSerializer
)
from api.permissions import KPIDefinitionPermission, IsMoEStaffOrReadOnly


class TagViewSet(viewsets.ModelViewSet):
    """
    API endpoint for tags.
    Only MoE staff can create, update, or delete tags.
    """
    queryset = Tag.objects.all()
    serializer_class = TagSerializer
    permission_classes = [permissions.IsAuthenticated, KPIDefinitionPermission]


class AcademicYearViewSet(viewsets.ModelViewSet):
    """
    API endpoint for academic years.
    Only MoE staff can create, update, or delete academic years.
    """
    queryset = AcademicYear.objects.all()
    serializer_class = AcademicYearSerializer
    permission_classes = [permissions.IsAuthenticated, KPIDefinitionPermission]

    def get_queryset(self):
        """Filter academic years by is_current if requested."""
        queryset = AcademicYear.objects.all()

        # Filter by current status
        is_current = self.request.query_params.get('is_current')
        if is_current is not None:
            is_current = is_current.lower() == 'true'
            queryset = queryset.filter(is_current=is_current)

        return queryset

    def perform_create(self, serializer):
        """Handle the case where a new academic year is set as current."""
        # If the new academic year is being set as current
        if serializer.validated_data.get('is_current', False):
            # The model's save method will handle ensuring only one is current
            pass
        serializer.save()

    def perform_update(self, serializer):
        """Handle the case where an academic year is set as current."""
        # If the academic year is being set as current
        if serializer.validated_data.get('is_current', False):
            # The model's save method will handle ensuring only one is current
            pass
        serializer.save()

    @action(detail=True, methods=['post'])
    def set_current(self, request, pk=None):
        """Set the specified academic year as the current one."""
        if connection.schema_name != get_public_schema_name():
            return Response(
                {"detail": "This action can only be performed in the public schema."},
                status=status.HTTP_403_FORBIDDEN
            )

        # Check for confirmation
        confirmation = request.data.get('confirm', False)
        if not confirmation:
            return Response(
                {"detail": "This action requires confirmation. Please set 'confirm' to true in your request."},
                status=status.HTTP_400_BAD_REQUEST
            )

        academic_year = self.get_object()
        # The model's save method will handle ensuring only one is current
        academic_year.is_current = True
        academic_year.save()

        return Response({
            "status": "success",
            "message": f"{academic_year.name} set as current academic year.",
            "data": AcademicYearSerializer(academic_year).data
        })


class ThemeViewSet(viewsets.ModelViewSet):
    """
    API endpoint for themes.
    Only MoE staff can create, update, or delete themes.
    """
    queryset = Theme.objects.all()
    serializer_class = ThemeSerializer
    permission_classes = [permissions.IsAuthenticated, KPIDefinitionPermission]

    def get_serializer_context(self):
        """Add request to serializer context."""
        context = super().get_serializer_context()
        context['request'] = self.request
        return context

    def perform_create(self, serializer):
        """Set the created_by field to the current user."""
        serializer.save(created_by=self.request.user)

    def retrieve(self, request, *args, **kwargs):
        """Get a theme with its subthemes."""
        instance = self.get_object()
        serializer = self.get_serializer(instance)
        data = serializer.data

        # Add subthemes to the response
        subthemes = SubTheme.objects.filter(theme=instance)
        subtheme_serializer = SubThemeSerializer(subthemes, many=True)
        data['subthemes'] = subtheme_serializer.data

        # Add some KPIs to the response
        kpis = KPI.objects.filter(theme=instance)[:5]  # Limit to 5 KPIs
        kpi_serializer = KPISerializer(kpis, many=True)
        data['kpis'] = kpi_serializer.data

        return Response(data)


class SubThemeViewSet(viewsets.ModelViewSet):
    """
    API endpoint for subthemes.
    Only MoE staff can create, update, or delete subthemes.
    """
    queryset = SubTheme.objects.all()
    serializer_class = SubThemeSerializer
    permission_classes = [permissions.IsAuthenticated, KPIDefinitionPermission]

    def get_serializer_context(self):
        """Add request to serializer context."""
        context = super().get_serializer_context()
        context['request'] = self.request
        return context

    def perform_create(self, serializer):
        """Set the created_by field to the current user."""
        serializer.save(created_by=self.request.user)

    def get_queryset(self):
        """Filter subthemes by theme if theme parameter is provided."""
        queryset = SubTheme.objects.all()

        # Check for theme parameter (API uses 'theme' not 'theme_id')
        theme = self.request.query_params.get('theme')
        if theme:
            queryset = queryset.filter(theme_id=theme)

        # Also check for theme_id for backward compatibility
        theme_id = self.request.query_params.get('theme_id')
        if theme_id:
            queryset = queryset.filter(theme_id=theme_id)

        return queryset

    def create(self, request, *args, **kwargs):
        """Custom create method with better error handling."""
        import logging
        logger = logging.getLogger(__name__)

        # Log the request data for debugging
        logger.debug(f"SubTheme create request data: {request.data}")

        # Ensure theme_id is provided
        if 'theme' not in request.data and 'theme_id' not in request.data:
            return Response(
                {"theme": ["Theme is required."]},
                status=status.HTTP_400_BAD_REQUEST
            )

        # If theme_id is provided but theme is not, convert it
        if 'theme_id' in request.data and 'theme' not in request.data:
            request.data['theme'] = request.data['theme_id']

        serializer = self.get_serializer(data=request.data)
        try:
            serializer.is_valid(raise_exception=True)
            self.perform_create(serializer)
            headers = self.get_success_headers(serializer.data)
            return Response(serializer.data, status=status.HTTP_201_CREATED, headers=headers)
        except Exception as e:
            logger.error(f"Error creating SubTheme: {str(e)}")
            if hasattr(serializer, 'errors'):
                logger.error(f"Serializer errors: {serializer.errors}")
                return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
            return Response({"detail": str(e)}, status=status.HTTP_400_BAD_REQUEST)

    def update(self, request, *args, **kwargs):
        """Custom update method with better error handling."""
        import logging
        logger = logging.getLogger(__name__)

        # Log the request data for debugging
        logger.debug(f"SubTheme update request data: {request.data}")

        # If theme_id is provided but theme is not, convert it
        if 'theme_id' in request.data and 'theme' not in request.data:
            request.data['theme'] = request.data['theme_id']

        try:
            return super().update(request, *args, **kwargs)
        except Exception as e:
            logger.error(f"Error updating SubTheme: {str(e)}")
            return Response({"detail": str(e)}, status=status.HTTP_400_BAD_REQUEST)

    def retrieve(self, request, *args, **kwargs):
        """Get a subtheme with its KPIs."""
        instance = self.get_object()
        serializer = self.get_serializer(instance)
        data = serializer.data

        # Add KPIs to the response
        kpis = KPI.objects.filter(sub_theme=instance)
        kpi_serializer = KPISerializer(kpis, many=True)
        data['kpis'] = kpi_serializer.data

        # Add theme details
        theme = instance.theme
        theme_serializer = ThemeSerializer(theme)
        data['theme'] = theme_serializer.data

        return Response(data)

    @action(detail=True, methods=['get'])
    def kpis(self, request, pk=None):
        """Get KPIs for a specific subtheme."""
        subtheme = self.get_object()
        kpis = KPI.objects.filter(sub_theme=subtheme)
        serializer = KPISerializer(kpis, many=True)
        return Response(serializer.data)


class MeasurementUnitViewSet(viewsets.ModelViewSet):
    """
    API endpoint for measurement units.
    Only MoE staff can create, update, or delete measurement units.
    """
    queryset = MeasurementUnit.objects.all()
    serializer_class = MeasurementUnitSerializer
    permission_classes = [permissions.IsAuthenticated, KPIDefinitionPermission]


class KPIViewSet(viewsets.ModelViewSet):
    """
    API endpoint for KPIs.
    Only MoE staff can create, update, or delete KPIs.
    """
    queryset = KPI.objects.all()
    serializer_class = KPISerializer
    permission_classes = [permissions.IsAuthenticated, KPIDefinitionPermission]

    def get_serializer_context(self):
        """Add request to serializer context."""
        context = super().get_serializer_context()
        context['request'] = self.request
        return context

    def perform_create(self, serializer):
        """Set the created_by field to the current user."""
        serializer.save(created_by=self.request.user)

    def perform_update(self, serializer):
        """Set the approved_by and approved_at fields if status is being changed to approved."""
        instance = self.get_object()
        data = serializer.validated_data

        # If status is being changed to approved and it wasn't approved before
        if 'status' in data and data['status'] == 'approved' and instance.status != 'approved':
            from django.utils import timezone
            serializer.save(approved_by=self.request.user, approved_at=timezone.now())
        else:
            serializer.save()

    @action(detail=True, methods=['get'])
    def performance(self, request, pk=None):
        """
        Get performance data for a specific KPI.

        Returns:
            - universityCount: Number of universities assigned to this KPI
            - activeAssignments: Number of active assignments for this KPI
            - averageTarget: Average target value across all assignments
            - completionRate: Percentage of reports submitted for this KPI
        """
        import logging
        logger = logging.getLogger(__name__)

        try:
            kpi = self.get_object()

            # Get assignments for this KPI
            from kpi_assignments.models import UniversityKPIAssignment
            assignments = UniversityKPIAssignment.objects.filter(kpi=kpi)

            # Count universities
            university_count = assignments.values('university').distinct().count()

            # Count active assignments
            active_assignments = assignments.filter(is_active=True).count()

            # Calculate average target
            from django.db.models import Avg
            avg_target = assignments.aggregate(avg_target=Avg('target_value'))['avg_target']

            # Format average target
            if avg_target is not None:
                try:
                    avg_target = float(avg_target)
                    # Format to 2 decimal places if it's a number
                    avg_target = f"{avg_target:.2f}"
                except (ValueError, TypeError):
                    avg_target = str(avg_target)
            else:
                avg_target = 'N/A'

            # Calculate completion rate
            from kpi_assignments.models import KPIReport
            total_reports = KPIReport.objects.filter(university_assignment__kpi=kpi).count()
            submitted_reports = KPIReport.objects.filter(
                university_assignment__kpi=kpi,
                status__in=['submitted', 'under_review', 'approved']
            ).count()

            completion_rate = 0
            if total_reports > 0:
                completion_rate = (submitted_reports / total_reports) * 100

            # Return performance data
            return Response({
                'universityCount': university_count,
                'activeAssignments': active_assignments,
                'averageTarget': avg_target,
                'completionRate': completion_rate,
                'totalReports': total_reports,
                'submittedReports': submitted_reports
            })
        except Exception as e:
            logger.error(f"Error getting KPI performance data: {str(e)}")
            return Response(
                {"error": f"Failed to get performance data: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def get_queryset(self):
        """Filter KPIs by theme, subtheme, or academic year if provided."""
        queryset = KPI.objects.all()

        theme_id = self.request.query_params.get('theme_id')
        if theme_id:
            queryset = queryset.filter(theme_id=theme_id)

        subtheme_id = self.request.query_params.get('subtheme_id')
        if subtheme_id:
            queryset = queryset.filter(sub_theme_id=subtheme_id)

        academic_year_id = self.request.query_params.get('academic_year_id')
        if academic_year_id:
            queryset = queryset.filter(academic_year_id=academic_year_id)

        status_filter = self.request.query_params.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)

        is_active = self.request.query_params.get('is_active')
        if is_active is not None:
            is_active = is_active.lower() == 'true'
            queryset = queryset.filter(is_active=is_active)

        return queryset

    @action(detail=False, methods=['get'], permission_classes=[permissions.AllowAny])
    def download_template(self, request):
        """Download KPI import template."""
        from api.imports import generate_kpi_template

        # Use the existing template generator function
        return generate_kpi_template()

    @action(detail=False, methods=['post'])
    def import_kpis(self, request):
        """Import KPIs from Excel file."""
        import pandas as pd
        import io
        import logging
        from django.db import transaction
        from django.utils import timezone

        logger = logging.getLogger(__name__)

        # Check if file was uploaded
        if 'file' not in request.FILES:
            return Response({'error': 'No file uploaded'}, status=status.HTTP_400_BAD_REQUEST)

        file = request.FILES['file']

        # Check file extension
        if not file.name.endswith(('.xlsx', '.xls')):
            return Response({'error': 'File must be an Excel file (.xlsx or .xls)'},
                           status=status.HTTP_400_BAD_REQUEST)

        # Process the file
        try:
            # Read Excel file
            df = pd.read_excel(file)

            # Check required columns
            required_columns = ['Title', 'Description', 'Theme Name', 'Measurement Unit',
                               'Frequency', 'Direction', 'Academic Year']

            missing_columns = [col for col in required_columns if col not in df.columns]
            if missing_columns:
                return Response(
                    {'error': f'Missing required columns: {", ".join(missing_columns)}'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Initialize counters
            created_count = 0
            updated_count = 0
            error_count = 0
            errors = []
            created_themes = []
            created_subthemes = []
            created_units = []

            # Process each row
            with transaction.atomic():
                for index, row in df.iterrows():
                    try:
                        # Check required fields
                        if pd.isna(row['Title']) or pd.isna(row['Description']) or pd.isna(row['Theme Name']):
                            error_count += 1
                            errors.append({
                                'row': index + 2,  # +2 because Excel is 1-indexed and has header row
                                'error': 'Missing required fields (Title, Description, or Theme Name)',
                                'data': row.to_dict()
                            })
                            continue

                        # Get or create theme
                        theme_name = row['Theme Name'].strip()
                        theme, theme_created = Theme.objects.get_or_create(
                            name=theme_name,
                            defaults={
                                'description': f'Theme for {theme_name}',
                                'created_by': request.user
                            }
                        )

                        if theme_created:
                            created_themes.append(theme_name)

                        # Get or create subtheme if provided
                        sub_theme = None
                        if 'SubTheme Name' in row and pd.notna(row['SubTheme Name']):
                            subtheme_name = row['SubTheme Name'].strip()
                            sub_theme, subtheme_created = SubTheme.objects.get_or_create(
                                name=subtheme_name,
                                theme=theme,
                                defaults={
                                    'description': f'SubTheme for {subtheme_name}',
                                    'created_by': request.user
                                }
                            )

                            if subtheme_created:
                                created_subthemes.append(subtheme_name)

                        # Get or create measurement unit
                        unit_name = row['Measurement Unit'].strip()
                        unit, unit_created = MeasurementUnit.objects.get_or_create(
                            name=unit_name,
                            defaults={
                                'symbol': unit_name[:3].upper(),
                                'description': f'Unit for {unit_name}'
                            }
                        )

                        if unit_created:
                            created_units.append(unit_name)

                        # Get or create academic year
                        academic_year_name = row['Academic Year'].strip()
                        try:
                            academic_year = AcademicYear.objects.get(name=academic_year_name)
                        except AcademicYear.DoesNotExist:
                            # Create a default academic year if it doesn't exist
                            current_year = timezone.now().year
                            academic_year = AcademicYear.objects.create(
                                name=academic_year_name,
                                start_date=timezone.datetime(current_year, 9, 1).date(),
                                end_date=timezone.datetime(current_year + 1, 8, 31).date(),
                                description=f'Academic year {academic_year_name}'
                            )

                        # Get status (default to draft if not provided or invalid)
                        status_value = 'draft'
                        if 'Status' in row and pd.notna(row['Status']):
                            status_input = row['Status'].strip().lower()
                            valid_statuses = [choice[0] for choice in KPI.STATUS_CHOICES]
                            if status_input in valid_statuses:
                                status_value = status_input

                        # Get frequency (default to annually if not provided or invalid)
                        frequency = 'annually'
                        if pd.notna(row['Frequency']):
                            frequency_input = row['Frequency'].strip().lower()
                            valid_frequencies = [choice[0] for choice in KPI.FREQUENCY_CHOICES]
                            if frequency_input in valid_frequencies:
                                frequency = frequency_input

                        # Get direction (default to increase if not provided or invalid)
                        direction = 'increase'
                        if pd.notna(row['Direction']):
                            direction_input = row['Direction'].strip().lower()
                            valid_directions = [choice[0] for choice in KPI.DIRECTION_CHOICES]
                            if direction_input in valid_directions:
                                direction = direction_input

                        # Prepare KPI data
                        kpi_data = {
                            'title': row['Title'].strip(),
                            'description': row['Description'].strip(),
                            'theme': theme,
                            'sub_theme': sub_theme,
                            'measurement_unit': unit,
                            'frequency': frequency,
                            'direction': direction,
                            'academic_year': academic_year,
                            'status': status_value,
                            'calculation_method': row.get('Calculation Method', '').strip() if pd.notna(row.get('Calculation Method', '')) else '',
                            'data_source': row.get('Data Source', '').strip() if pd.notna(row.get('Data Source', '')) else '',
                            'responsible_department': row.get('Responsible Department', '').strip() if pd.notna(row.get('Responsible Department', '')) else '',
                            'responsible_person': row.get('Responsible Person', '').strip() if pd.notna(row.get('Responsible Person', '')) else '',
                            'contact_email': row.get('Contact Email', '').strip() if pd.notna(row.get('Contact Email', '')) else '',
                            'is_active': True,
                            'created_by': request.user
                        }

                        # Check if KPI already exists (by title and theme)
                        existing_kpi = KPI.objects.filter(
                            title=kpi_data['title'],
                            theme=theme
                        ).first()

                        if existing_kpi:
                            # Update existing KPI
                            for key, value in kpi_data.items():
                                if value or key in ['sub_theme']:  # Allow sub_theme to be None
                                    setattr(existing_kpi, key, value)
                            existing_kpi.save()
                            updated_count += 1
                        else:
                            # Create new KPI
                            KPI.objects.create(**kpi_data)
                            created_count += 1

                    except Exception as e:
                        error_count += 1
                        errors.append({
                            'row': index + 2,  # +2 because Excel is 1-indexed and has header row
                            'error': str(e),
                            'data': row.to_dict()
                        })
                        logger.error(f"Error importing KPI at row {index + 2}: {str(e)}")

            # Return results
            return Response({
                'success': True,
                'created': created_count,
                'updated': updated_count,
                'errors': error_count,
                'error_details': errors if errors else None,
                'created_themes': created_themes,
                'created_subthemes': created_subthemes,
                'created_units': created_units
            }, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"Error importing KPIs: {str(e)}")
            return Response({'error': str(e)}, status=status.HTTP_400_BAD_REQUEST)


class ReportTemplateViewSet(viewsets.ModelViewSet):
    """
    API endpoint for report templates.
    Authenticated users can view templates.
    Users can create, update, and delete their own templates.
    """
    queryset = ReportTemplate.objects.all()
    serializer_class = ReportTemplateSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        """Filter templates by active status or created_by if requested."""
        queryset = ReportTemplate.objects.all()

        # Filter by active status
        is_active = self.request.query_params.get('is_active')
        if is_active is not None:
            is_active = is_active.lower() == 'true'
            queryset = queryset.filter(is_active=is_active)

        # Filter by creator
        created_by = self.request.query_params.get('created_by')
        if created_by is not None:
            queryset = queryset.filter(created_by_id=created_by)

        # Filter by current user's templates
        my_templates = self.request.query_params.get('my_templates')
        if my_templates is not None and my_templates.lower() == 'true':
            queryset = queryset.filter(created_by=self.request.user)

        return queryset

    def perform_create(self, serializer):
        """Set the created_by field to the current user."""
        serializer.save(created_by=self.request.user)

    @action(detail=True, methods=['post'])
    def duplicate(self, request, pk=None):
        """Duplicate a template."""
        template = self.get_object()
        new_title = request.data.get('new_title', f"Copy of {template.title}")

        # Create a duplicate
        new_template = template.duplicate(new_title)

        # Return the new template data
        serializer = self.get_serializer(new_template)
        return Response(serializer.data, status=status.HTTP_201_CREATED)