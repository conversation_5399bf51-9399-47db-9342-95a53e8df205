#!/usr/bin/env python3
"""
Fix authentication issues by creating proper admin users and testing login.
"""

import os
import django
import sys

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'moe_kpi_tracker.settings')
django.setup()

from django.contrib.auth.models import User
from django_tenants.utils import schema_context, tenant_context
from universities.models import University
import requests
import json

def create_admin_users():
    """Create admin users in all schemas."""
    print("=== Creating Admin Users ===")
    
    # Create admin in public schema
    with schema_context('public'):
        print("Creating admin in public schema...")
        
        # Delete existing admin if exists
        User.objects.filter(username='admin').delete()
        
        # Create new admin
        admin_user = User.objects.create_superuser(
            username='admin',
            email='<EMAIL>',
            password='admin123',
            first_name='Admin',
            last_name='MoE'
        )
        print(f"Created admin user in public schema: {admin_user.username}")
        print(f"Email: {admin_user.email}")
        print(f"Password: admin123")
    
    # Create admin in each university schema
    universities = University.objects.exclude(schema_name='public')
    for university in universities:
        with tenant_context(university):
            print(f"\nCreating admin in {university.schema_name} schema...")
            
            # Delete existing admin if exists
            User.objects.filter(username='admin').delete()
            
            # Create new admin
            admin_user = User.objects.create_superuser(
                username='admin',
                email=f'admin@{university.schema_name}.edu.et',
                password='admin123',
                first_name='Admin',
                last_name=university.name
            )
            print(f"Created admin user in {university.schema_name} schema: {admin_user.username}")
            print(f"Email: {admin_user.email}")
            print(f"Password: admin123")

def test_authentication():
    """Test authentication with the created admin users."""
    print("\n=== Testing Authentication ===")
    
    base_url = "http://localhost:8000/api/v1"
    
    # Test public tenant login
    print("\nTesting public tenant login...")
    try:
        response = requests.post(
            f"{base_url}/auth/token/",
            json={
                "username": "<EMAIL>",
                "password": "admin123"
            },
            headers={
                "Content-Type": "application/json",
                "X-Tenant": "public"
            }
        )
        
        if response.status_code == 200:
            print("✅ Public tenant login successful!")
            token_data = response.json()
            print(f"Access token received: {token_data.get('access', 'N/A')[:50]}...")
            
            # Test user endpoint
            user_response = requests.get(
                f"{base_url}/auth/user/",
                headers={
                    "Authorization": f"Bearer {token_data['access']}",
                    "X-Tenant": "public"
                }
            )
            
            if user_response.status_code == 200:
                print("✅ User endpoint accessible!")
                user_data = user_response.json()
                print(f"User: {user_data.get('username')} ({user_data.get('email')})")
            else:
                print(f"❌ User endpoint failed: {user_response.status_code}")
                print(f"Response: {user_response.text}")
        else:
            print(f"❌ Public tenant login failed: {response.status_code}")
            print(f"Response: {response.text}")
    except Exception as e:
        print(f"❌ Error testing public tenant: {e}")
    
    # Test university tenant login
    universities = University.objects.exclude(schema_name='public')
    if universities.exists():
        university = universities.first()
        print(f"\nTesting {university.schema_name} tenant login...")
        
        try:
            response = requests.post(
                f"{base_url}/auth/token/",
                json={
                    "username": f"admin@{university.schema_name}.edu.et",
                    "password": "admin123"
                },
                headers={
                    "Content-Type": "application/json",
                    "X-Tenant": university.schema_name
                }
            )
            
            if response.status_code == 200:
                print(f"✅ {university.schema_name} tenant login successful!")
                token_data = response.json()
                print(f"Access token received: {token_data.get('access', 'N/A')[:50]}...")
            else:
                print(f"❌ {university.schema_name} tenant login failed: {response.status_code}")
                print(f"Response: {response.text}")
        except Exception as e:
            print(f"❌ Error testing {university.schema_name} tenant: {e}")

def print_login_instructions():
    """Print login instructions for users."""
    print("\n=== Login Instructions ===")
    print("\n🏛️  Ministry of Education Dashboard:")
    print("   Email: <EMAIL>")
    print("   Password: admin123")
    print("   This will show the comprehensive MoE dashboard")
    
    print("\n🏫 University Dashboards:")
    universities = University.objects.exclude(schema_name='public')
    for university in universities:
        print(f"   {university.name}:")
        print(f"   Email: admin@{university.schema_name}.edu.et")
        print(f"   Password: admin123")
    
    print("\n📝 Notes:")
    print("   - The system automatically detects tenant from email domain")
    print("   - MoE emails (@moe.gov.et) access the public tenant dashboard")
    print("   - University emails (@university.edu.et) access university-specific dashboards")
    print("   - The dashboard shown depends on the tenant type")

if __name__ == "__main__":
    try:
        create_admin_users()
        test_authentication()
        print_login_instructions()
        print("\n✅ Authentication setup complete!")
    except Exception as e:
        print(f"❌ Error: {e}")
        sys.exit(1)
